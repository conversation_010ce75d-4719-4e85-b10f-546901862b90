# Relatório Final: <PERSON><PERSON>id/Ask no yfinance

**Data:** 23 de Junho de 2025  
**Descoberta:** yfinance FORNECE dados de bid/ask para ações brasileiras!  
**Status:** ✅ CONFIRMADO E IMPLEMENTADO

---

## 🎯 Descoberta Principal

**CONFIRMADO:** O yfinance possui dados de bid/ask disponíveis através do método `ticker.info`!

### Dados Disponíveis:
- ✅ **bid**: Preço de compra atual
- ✅ **ask**: Preço de venda atual  
- ✅ **bidSize**: Quantidade no bid (limitado para ações brasileiras)
- ✅ **askSize**: Quantidade no ask (limitado para ações brasileiras)
- ✅ **Spread**: <PERSON><PERSON><PERSON><PERSON>vel (ask - bid)

---

## 📊 Resultados dos Testes

### Ações Americanas (Exemplo: AAPL)
```
Bid: $201.38
Ask: $201.64
Bid Size: 2
Ask Size: 2
Spread: $0.26 (0.129%)
```

### Ações Brasileiras (Exemplo: PETR4.SA)
```
Bid: R$ 31.96
Ask: R$ 31.97
Bid Size: 0 (não disponível)
Ask Size: 0 (não disponível)
Spread: R$ 0.01 (0.031%)
```

### Opções (Apenas ações americanas)
```
AAPL Calls:
- Strike $90: Bid $90.70, Ask $91.40
- Strike $80: Bid $80.65, Ask $82.15

AAPL Puts:
- Strike $200: Bid $0.00, Ask $0.01
```

---

## 💻 Implementação Prática

### Código Básico
```python
import yfinance as yf

def get_bid_ask_data(ticker_symbol):
    ticker = yf.Ticker(ticker_symbol)
    info = ticker.info
    
    bid = info.get('bid')
    ask = info.get('ask')
    
    if bid and ask:
        spread = ask - bid
        spread_pct = (spread / bid) * 100
        
        return {
            'symbol': ticker_symbol,
            'bid': bid,
            'ask': ask,
            'spread': spread,
            'spread_pct': spread_pct
        }
    return None

# Exemplo de uso
data = get_bid_ask_data('PETR4.SA')
print(f"Spread: {data['spread_pct']:.4f}%")
```

### Análise de Carteira
```python
def analyze_portfolio_spreads(tickers):
    results = []
    for ticker in tickers:
        data = get_bid_ask_data(ticker)
        if data:
            results.append(data)
    
    # Ordenar por menor spread (melhor liquidez)
    results.sort(key=lambda x: x['spread_pct'])
    return results

# Ações brasileiras com melhor liquidez
brazilian_stocks = ['PETR4.SA', 'VALE3.SA', 'ITUB4.SA', 'BBDC4.SA']
liquidity_analysis = analyze_portfolio_spreads(brazilian_stocks)
```

---

## 📈 Integração com Análise Kalman

### Exemplo de Integração
```python
import yfinance as yf
import numpy as np
from kalman import KalmanFilter  # Seu filtro customizado

def enhanced_kalman_with_spread(ticker_symbol):
    # 1. Obter dados históricos
    ticker = yf.Ticker(ticker_symbol)
    hist = ticker.history(period="1y")
    
    # 2. Obter bid/ask atual
    info = ticker.info
    current_bid = info.get('bid')
    current_ask = info.get('ask')
    
    if current_bid and current_ask:
        current_spread = ((current_ask - current_bid) / current_bid) * 100
        
        # 3. Usar spread como indicador de confiança
        # Spread baixo = alta confiança, spread alto = baixa confiança
        confidence_factor = 1 / (1 + current_spread)
        
        # 4. Aplicar Kalman com fator de confiança
        kf = KalmanFilter()
        kf.set_confidence(confidence_factor)
        
        # 5. Processar dados
        filtered_prices = kf.filter(hist['Close'].values)
        
        return {
            'filtered_prices': filtered_prices,
            'current_spread': current_spread,
            'confidence': confidence_factor,
            'bid': current_bid,
            'ask': current_ask
        }
    
    return None
```

---

## 🚀 Aplicações Práticas

### 1. **Alertas de Liquidez**
```python
def liquidity_alert(ticker_symbol, max_spread_pct=0.5):
    data = get_bid_ask_data(ticker_symbol)
    if data and data['spread_pct'] > max_spread_pct:
        print(f"⚠️ ALERTA: {ticker_symbol} com spread alto: {data['spread_pct']:.4f}%")
        return True
    return False
```

### 2. **Otimização de Entrada/Saída**
```python
def optimal_entry_exit(ticker_symbol):
    ticker = yf.Ticker(ticker_symbol)
    info = ticker.info
    
    bid = info.get('bid')
    ask = info.get('ask')
    current_price = info.get('currentPrice')
    
    if bid and ask and current_price:
        # Estratégia: comprar próximo ao bid, vender próximo ao ask
        buy_target = bid + (ask - bid) * 0.3  # 30% do spread acima do bid
        sell_target = ask - (ask - bid) * 0.3  # 30% do spread abaixo do ask
        
        return {
            'current_price': current_price,
            'buy_target': buy_target,
            'sell_target': sell_target,
            'spread_pct': ((ask - bid) / bid) * 100
        }
    return None
```

### 3. **Monitoramento em Tempo Real**
```python
import time
from datetime import datetime

def monitor_spread_changes(ticker_symbol, duration_minutes=60):
    spreads = []
    start_time = datetime.now()
    
    while (datetime.now() - start_time).seconds < duration_minutes * 60:
        data = get_bid_ask_data(ticker_symbol)
        if data:
            spreads.append({
                'timestamp': datetime.now(),
                'spread_pct': data['spread_pct']
            })
            print(f"{datetime.now().strftime('%H:%M:%S')} - Spread: {data['spread_pct']:.4f}%")
        
        time.sleep(30)  # Atualizar a cada 30 segundos
    
    return spreads
```

---

## 📊 Limitações Identificadas

### ❌ **Limitações**
1. **Bid/Ask Size**: Sempre 0 para ações brasileiras
2. **Dados Históricos**: Apenas snapshot atual, não histórico de bid/ask
3. **Opções**: Disponível apenas para ações americanas
4. **Rate Limiting**: Cuidado com muitas chamadas seguidas

### ✅ **Pontos Fortes**
1. **Disponibilidade**: Funciona para ações brasileiras e americanas
2. **Precisão**: Dados em tempo real
3. **Facilidade**: Integração simples com código existente
4. **Gratuito**: Sem custos adicionais

---

## 🎯 Recomendações Finais

### Para Seu Projeto Atual:
1. **Continue usando yfinance** - agora com dados de bid/ask!
2. **Integre spread ao filtro Kalman** como fator de confiança
3. **Monitore liquidez** das ações da carteira
4. **Crie alertas** para spreads anômalos

### Implementação Sugerida:
```python
# Adicionar ao seu script principal
def enhanced_stock_analysis(ticker_symbol):
    # Análise existente (Kalman, correlação, etc.)
    # + Nova análise de bid/ask
    
    bid_ask_data = get_bid_ask_data(ticker_symbol)
    
    if bid_ask_data:
        # Usar spread como indicador de liquidez
        liquidity_score = 1 / (1 + bid_ask_data['spread_pct'])
        
        # Integrar ao sistema existente
        return {
            'kalman_analysis': your_existing_kalman_analysis(),
            'correlation_analysis': your_existing_correlation_analysis(),
            'liquidity_analysis': bid_ask_data,
            'liquidity_score': liquidity_score
        }
```

---

## 📁 Arquivos Criados

1. `src/test_yfinance_comprehensive.py` - Teste exaustivo que descobriu os dados
2. `src/yfinance_bid_ask_extractor.py` - Extrator completo de bid/ask
3. `src/bid_ask_acoes_brasileiras.py` - Foco em ações brasileiras
4. `results/csv/yfinance_bid_ask_final_report.md` - Este relatório

---

**Conclusão:** A busca por dados de bid/ask foi **BEM-SUCEDIDA**! O yfinance fornece esses dados através do `ticker.info`, permitindo análises de liquidez e otimização de estratégias de trading. 🎉
