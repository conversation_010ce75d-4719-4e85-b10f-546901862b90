# Filtro de Kalman com Data de Corte

## 📋 Visão Geral

Esta funcionalidade permite escolher uma data específica para limitar os dados de treinamento do filtro de <PERSON>, **mantendo sempre a visualização completa dos dados reais** para comparação. Isso é útil para:

- **Testes de precisão**: Validar previsões comparando com dados reais
- **Backtesting**: Simular análises em datas passadas
- **Validação de modelo**: Testar a performance do filtro
- **Análise histórica**: Entender como o modelo se comportaria em períodos específicos

## 🎯 Funcionalidade Principal

**IMPORTANTE**: Quando você especifica uma data de corte:
- ✅ O filtro de Kalman é treinado apenas com dados até a data especificada
- ✅ Os gráficos sempre mostram os dados reais completos até hoje
- ✅ Uma linha vermelha marca a data de corte no gráfico
- ✅ Você pode comparar visualmente as previsões com o que realmente aconteceu

## 🚀 Como Usar

### 1. Análise Principal com Data de Corte

Execute o script principal:

```bash
python src/analise_kalman_acoes_diversificadas.py
```

Quando perguntado sobre a configuração de data:
- Escolha opção **2** para especificar uma data de corte
- Digite a data no formato `YYYY-MM-DD` (ex: `2024-01-15`)
- O filtro usará apenas dados até essa data para fazer previsões

### 2. Teste Simples

Para um teste rápido com uma ação:

```bash
python src/teste_data_corte_kalman.py
```

Este script:
- Testa com e sem data de corte
- Compara os resultados
- Gera gráficos de comparação

### 3. Validação de Previsões

Para validar a precisão das previsões:

```bash
python src/validacao_previsoes_kalman.py
```

Este script:
- Faz previsões em datas passadas
- Compara com valores reais observados
- Calcula métricas de erro (MAE, MAPE, RMSE)
- Gera relatório de precisão

### 4. Demonstração Visual Comparativa

Para ver uma comparação lado a lado:

```bash
python src/demo_comparacao_visual.py
```

Este script:
- Cria gráficos comparativos lado a lado
- Mostra dados completos vs dados com corte
- Destaca visualmente a área de teste
- Calcula precisão das previsões automaticamente

## 📊 Exemplo Prático

### Cenário: Teste de Precisão

1. **Data atual**: 2024-06-23
2. **Data de corte**: 2024-04-01
3. **Objetivo**: Ver se as previsões de abril foram precisas

```python
# O filtro usará dados até 01/04/2024
# Fará previsões para 20 dias à frente
# Comparará com os preços reais de abril-maio
```

### Interpretação dos Resultados

- **MAPE < 2%**: Previsões excelentes
- **MAPE 2-5%**: Previsões boas
- **MAPE 5-10%**: Previsões moderadas
- **MAPE > 10%**: Previsões com baixa precisão

## 🔧 Parâmetros Técnicos

### Função Principal

```python
obter_dados_com_kalman_spread(ticker, nome, data_corte=None)
```

**Parâmetros:**
- `ticker`: Símbolo da ação (ex: "PETR4.SA")
- `nome`: Nome da empresa
- `data_corte`: Data limite (string 'YYYY-MM-DD' ou datetime)
  - Se `None`: usa todos os dados disponíveis
  - Se especificada: usa apenas dados até essa data

### Comportamento

1. **Coleta de dados**: Obtém 15 meses de dados históricos
2. **Aplicação do corte**: Filtra dados até a data especificada
3. **Treinamento**: Filtro de Kalman usa apenas dados filtrados
4. **Previsão**: Gera previsões a partir da data de corte
5. **Validação**: Compara com dados reais (se disponíveis)

## 📈 Métricas de Avaliação

### MAE (Mean Absolute Error)
- Erro absoluto médio em reais
- Quanto menor, melhor
- Exemplo: MAE = R$ 2,50 significa erro médio de R$ 2,50

### MAPE (Mean Absolute Percentage Error)
- Erro percentual médio
- Mais fácil de interpretar
- Exemplo: MAPE = 3% significa erro médio de 3%

### RMSE (Root Mean Square Error)
- Penaliza erros grandes
- Útil para detectar outliers
- Em reais, como o MAE

### Correlação
- Mede relação linear entre previsão e realidade
- Varia de -1 a +1
- Valores próximos de +1 indicam boa correlação

## 📁 Estrutura de Arquivos

```
results/
├── figures/
│   ├── kalman_analysis/          # Gráficos principais
│   ├── validacao_kalman/         # Gráficos de validação
│   └── comparacao_visual/        # Gráficos comparativos lado a lado
└── csv/
    ├── kalman_analysis/          # Resultados principais
    └── validacao_kalman/         # Métricas de validação
```

## 🎯 Casos de Uso

### 1. Teste de Estratégia
```bash
# Simular análise em janeiro de 2024
Data de corte: 2024-01-31
# Ver se as previsões para fevereiro foram boas
```

### 2. Validação Mensal
```bash
# Testar previsões do mês passado
Data de corte: 2024-05-01
# Comparar com preços reais de maio
```

### 3. Análise de Crise
```bash
# Simular análise durante evento específico
Data de corte: 2024-03-15
# Ver como o modelo se comporta em períodos voláteis
```

## ⚠️ Limitações

1. **Dados mínimos**: Precisa de pelo menos 200 dias de dados após o corte
2. **Dias úteis**: Previsões consideram apenas dias de pregão
3. **Feriados**: Não considera feriados específicos do mercado
4. **Liquidez**: Ações com baixa liquidez podem ter previsões menos precisas

## 🔍 Troubleshooting

### Erro: "Dados insuficientes após corte"
- **Causa**: Data de corte muito recente
- **Solução**: Use data mais antiga (pelo menos 8-10 meses atrás)

### Erro: "Formato de data inválido"
- **Causa**: Data não está no formato correto
- **Solução**: Use formato YYYY-MM-DD (ex: 2024-01-15)

### Previsões muito imprecisas
- **Causa**: Período muito volátil ou ação com baixa liquidez
- **Solução**: Teste com ações mais líquidas ou períodos mais estáveis

## 📞 Suporte

Para dúvidas ou problemas:
1. Verifique os logs de erro no terminal
2. Confirme se a data está no formato correto
3. Teste com ações mais líquidas primeiro
4. Verifique se há dados suficientes no período escolhido
