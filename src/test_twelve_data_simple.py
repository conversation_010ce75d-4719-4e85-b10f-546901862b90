#!/usr/bin/env python3
"""
Teste simplificado do Twelve Data API - Foco em bid/ask volume
"""

import time
import requests
import pandas as pd

# Sua chave API do Twelve Data
API_KEY = "aadf74ae14d74fd7ac40a433dad50f51"

def test_quote_detailed(ticker):
    """
    Testa endpoint de cotação com análise detalhada
    """
    print(f"\n🔍 Testando QUOTE detalhado para {ticker}")
    
    try:
        url = f"https://api.twelvedata.com/quote?symbol={ticker}&apikey={API_KEY}"
        response = requests.get(url)
        
        if response.status_code == 200:
            data = response.json()
            
            # Verificar se há erro
            if 'code' in data and data['code'] != 200:
                print(f"   ❌ Erro: {data.get('message', 'Erro desconhecido')}")
                return False
            
            print("✅ Dados obtidos com sucesso!")
            print(f"   Símbolo: {data.get('symbol', 'N/A')}")
            print(f"   Nome: {data.get('name', 'N/A')}")
            print(f"   Exchange: {data.get('exchange', 'N/A')}")
            print(f"   Preço: ${data.get('close', 'N/A')}")
            print(f"   Volume: {data.get('volume', 'N/A')}")
            
            print(f"\n   📊 TODOS OS CAMPOS DISPONÍVEIS:")
            for key, value in data.items():
                print(f"      {key}: {value}")
                
            # Busca específica por bid/ask
            print(f"\n   🎯 BUSCA POR BID/ASK:")
            bid_ask_found = False
            for key, value in data.items():
                key_lower = key.lower()
                if any(term in key_lower for term in ['bid', 'ask', 'spread']):
                    print(f"      ✅ ENCONTRADO: {key} = {value}")
                    bid_ask_found = True
            
            if not bid_ask_found:
                print(f"      ❌ Nenhum campo bid/ask encontrado")
                
            return True
        else:
            print(f"   ❌ Erro HTTP: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erro: {e}")
        return False

def test_time_series_detailed(ticker):
    """
    Testa time series intraday para buscar bid/ask
    """
    print(f"\n📊 Testando TIME SERIES (1min) para {ticker}")
    
    try:
        url = f"https://api.twelvedata.com/time_series?symbol={ticker}&interval=1min&outputsize=5&apikey={API_KEY}"
        response = requests.get(url)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'code' in data and data['code'] != 200:
                print(f"   ❌ Erro: {data.get('message', 'Erro desconhecido')}")
                return False
            
            print("✅ Time series obtido!")
            
            if 'values' in data and data['values']:
                latest = data['values'][0]
                print(f"   📊 Campos no time series:")
                for key, value in latest.items():
                    print(f"      {key}: {value}")
                    
                # Buscar bid/ask
                bid_ask_found = False
                for key, value in latest.items():
                    if any(term in key.lower() for term in ['bid', 'ask', 'spread']):
                        print(f"      🎯 BID/ASK ENCONTRADO: {key} = {value}")
                        bid_ask_found = True
                
                if not bid_ask_found:
                    print(f"      ❌ Nenhum bid/ask no time series")
            else:
                print(f"   ❌ Sem dados de time series")
                
            return True
        else:
            print(f"   ❌ Erro HTTP: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erro: {e}")
        return False

def test_forex_for_bid_ask():
    """
    Testa forex que pode ter bid/ask spreads
    """
    print(f"\n💱 Testando FOREX (EUR/USD) - pode ter bid/ask")
    
    try:
        url = f"https://api.twelvedata.com/quote?symbol=EUR/USD&apikey={API_KEY}"
        response = requests.get(url)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'code' in data and data['code'] != 200:
                print(f"   ❌ Erro: {data.get('message', 'Erro desconhecido')}")
                return False
            
            print("✅ Dados forex obtidos!")
            print(f"   📊 Campos forex:")
            for key, value in data.items():
                print(f"      {key}: {value}")
                if any(term in key.lower() for term in ['bid', 'ask', 'spread']):
                    print(f"      🎯 BID/ASK ENCONTRADO: {key} = {value}")
                    
            return True
        else:
            print(f"   ❌ Erro HTTP: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erro: {e}")
        return False

def test_brazilian_stocks():
    """
    Testa ações brasileiras
    """
    print(f"\n🇧🇷 TESTANDO AÇÕES BRASILEIRAS")
    print("=" * 50)
    
    # Formatos para testar
    brazilian_stocks = [
        "PETR4.SA",
        "VALE3.SA", 
        "ITUB4.SA",
        "PBR",  # Petrobras ADR
        "VALE"  # Vale ADR
    ]
    
    for ticker in brazilian_stocks:
        print(f"\n🔍 Testando: {ticker}")
        success = test_quote_detailed(ticker)
        if success:
            print(f"   ✅ {ticker} funcionou!")
        time.sleep(1)

def main():
    """
    Teste principal do Twelve Data
    """
    print("🚀 TESTE TWELVE DATA - BUSCA ESPECÍFICA POR BID/ASK VOLUME")
    print("=" * 65)
    print(f"🔑 API Key: {API_KEY[:8]}...")
    print()
    
    # Teste 1: Ações americanas populares
    us_stocks = ["AAPL", "GOOGL", "MSFT"]
    
    for ticker in us_stocks:
        print(f"\n{'='*60}")
        print(f"🔍 TESTANDO: {ticker}")
        print(f"{'='*60}")
        
        # Teste quote
        test_quote_detailed(ticker)
        time.sleep(1)
        
        # Teste time series
        test_time_series_detailed(ticker)
        time.sleep(1)
        
        # Pausa entre ações
        time.sleep(2)
    
    # Teste 2: Forex (mais provável de ter bid/ask)
    test_forex_for_bid_ask()
    time.sleep(2)
    
    # Teste 3: Ações brasileiras
    test_brazilian_stocks()
    
    # Conclusão
    print(f"\n\n🎯 CONCLUSÃO TWELVE DATA")
    print("=" * 50)
    print("📊 Twelve Data foi testado extensivamente:")
    print("   • Quote endpoint")
    print("   • Time series intraday")
    print("   • Forex pairs")
    print("   • Ações brasileiras e americanas")
    print()
    print("❓ RESULTADO BID/ASK VOLUME:")
    print("   Se não foi encontrado acima, Twelve Data")
    print("   também não fornece bid/ask volume no plano gratuito.")
    print()
    print("💡 ALTERNATIVAS RECOMENDADAS:")
    print("   1. Interactive Brokers API (melhor opção)")
    print("   2. APIs de corretoras brasileiras")
    print("   3. Planos premium de provedores de dados")

if __name__ == "__main__":
    main()
