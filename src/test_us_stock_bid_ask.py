#!/usr/bin/env python3
"""
Teste rápido: Verificar se ações americanas têm dados bid/ask históricos
"""

import yfinance as yf
import warnings
warnings.filterwarnings('ignore')

def test_us_stock():
    """
    Testa ação americana para comparar com brasileira
    """
    print("🇺🇸 TESTE: Ação Americana (AAPL)")
    print("=" * 50)
    
    ticker_symbol = "AAPL"
    
    # Teste 1: download
    print("📊 Testando yf.download()...")
    try:
        data = yf.download(ticker_symbol, period="1mo", interval="1d")
        print(f"   Colunas: {list(data.columns)}")
        
        bid_ask_cols = [col for col in data.columns 
                       if any(term in str(col).lower() for term in ['bid', 'ask'])]
        
        if bid_ask_cols:
            print(f"   🎯 Bid/Ask encontrado: {bid_ask_cols}")
        else:
            print(f"   ❌ Sem bid/ask histórico")
            
    except Exception as e:
        print(f"   ❌ Erro: {e}")
    
    # Teste 2: info
    print("\n📊 Testando ticker.info()...")
    try:
        ticker = yf.Ticker(ticker_symbol)
        info = ticker.info
        
        bid = info.get('bid')
        ask = info.get('ask')
        
        if bid and ask:
            print(f"   ✅ Bid: ${bid}, Ask: ${ask}")
            print(f"   📈 Spread: ${ask - bid:.4f}")
        else:
            print(f"   ❌ Sem bid/ask atual")
            
    except Exception as e:
        print(f"   ❌ Erro: {e}")

if __name__ == "__main__":
    test_us_stock()
