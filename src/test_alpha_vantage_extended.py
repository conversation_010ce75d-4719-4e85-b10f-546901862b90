#!/usr/bin/env python3
"""
Extended test script for Alpha Vantage API - Testing additional endpoints for bid/ask data
Using stocks from acoes_diversificacao.csv with proper formatting
"""

import time
import pandas as pd
import requests
from datetime import datetime

API_KEY = "ELGIUR9A6KBUAP47"

def test_forex_endpoints():
    """
    Testa endpoints de forex que podem ter bid/ask spreads
    """
    print(f"\n💱 TESTANDO ENDPOINTS DE FOREX (podem ter bid/ask)")
    print("=" * 50)
    
    # Teste FX_INTRADAY
    print(f"\n🔍 Testando FX_INTRADAY (EUR/USD)")
    try:
        url = f"https://www.alphavantage.co/query?function=FX_INTRADAY&from_symbol=EUR&to_symbol=USD&interval=5min&apikey={API_KEY}"
        response = requests.get(url)
        data = response.json()
        
        if "Time Series FX (5min)" in data:
            time_series = data["Time Series FX (5min)"]
            latest_time = list(time_series.keys())[0]
            latest_data = time_series[latest_time]
            
            print("✅ Dados FX obtidos!")
            print(f"   Último horário: {latest_time}")
            print(f"   Campos disponíveis: {list(latest_data.keys())}")
            
            # Verificar se há bid/ask
            for key, value in latest_data.items():
                print(f"   {key}: {value}")
                if any(term in key.lower() for term in ['bid', 'ask']):
                    print(f"   🎯 ENCONTRADO: {key} = {value}")
        else:
            print(f"❌ Erro: {data}")
            
    except Exception as e:
        print(f"❌ Erro: {e}")
    
    time.sleep(2)
    
    # Teste CURRENCY_EXCHANGE_RATE
    print(f"\n🔍 Testando CURRENCY_EXCHANGE_RATE")
    try:
        url = f"https://www.alphavantage.co/query?function=CURRENCY_EXCHANGE_RATE&from_currency=USD&to_currency=BRL&apikey={API_KEY}"
        response = requests.get(url)
        data = response.json()
        
        print(f"Resposta completa: {data}")
        
        if "Realtime Currency Exchange Rate" in data:
            exchange_data = data["Realtime Currency Exchange Rate"]
            print("✅ Dados de câmbio obtidos!")
            
            for key, value in exchange_data.items():
                print(f"   {key}: {value}")
                if any(term in key.lower() for term in ['bid', 'ask']):
                    print(f"   🎯 ENCONTRADO: {key} = {value}")
        else:
            print(f"❌ Erro: {data}")
            
    except Exception as e:
        print(f"❌ Erro: {e}")

def test_brazilian_stocks_proper_format():
    """
    Testa ações brasileiras com formato correto para Alpha Vantage
    """
    print(f"\n🇧🇷 TESTANDO AÇÕES BRASILEIRAS (formato correto)")
    print("=" * 50)
    
    # Ações brasileiras que podem estar disponíveis na Alpha Vantage
    # Algumas podem estar listadas como ADRs ou com sufixos diferentes
    brazilian_stocks = [
        ("PETR4.SAO", "Petrobras PN"),
        ("VALE3.SAO", "Vale"),
        ("ITUB4.SAO", "Itaú"),
        ("PETR4", "Petrobras (sem sufixo)"),
        ("VALE3", "Vale (sem sufixo)"),
        ("PBR", "Petrobras ADR"),
        ("VALE", "Vale ADR"),
        ("ITUB", "Itaú ADR")
    ]
    
    for ticker, nome in brazilian_stocks:
        print(f"\n🔍 Testando: {ticker} - {nome}")
        try:
            url = f"https://www.alphavantage.co/query?function=GLOBAL_QUOTE&symbol={ticker}&apikey={API_KEY}"
            response = requests.get(url)
            data = response.json()
            
            if "Global Quote" in data and data["Global Quote"]:
                quote = data["Global Quote"]
                symbol = quote.get('01. symbol', 'N/A')
                price = quote.get('05. price', 'N/A')
                volume = quote.get('06. volume', 'N/A')
                
                if symbol != 'N/A' and price != 'N/A':
                    print(f"   ✅ SUCESSO! Símbolo: {symbol}, Preço: ${price}, Volume: {volume}")
                else:
                    print(f"   ❌ Dados vazios")
            else:
                print(f"   ❌ Não encontrado: {data}")
                
        except Exception as e:
            print(f"   ❌ Erro: {e}")
        
        time.sleep(1)

def test_premium_endpoints():
    """
    Testa endpoints que podem ser premium mas vale a pena verificar
    """
    print(f"\n💎 TESTANDO ENDPOINTS PREMIUM/ESPECIAIS")
    print("=" * 50)
    
    # Lista de endpoints para testar
    premium_tests = [
        {
            "name": "EARNINGS",
            "url": f"https://www.alphavantage.co/query?function=EARNINGS&symbol=AAPL&apikey={API_KEY}"
        },
        {
            "name": "OVERVIEW",
            "url": f"https://www.alphavantage.co/query?function=OVERVIEW&symbol=AAPL&apikey={API_KEY}"
        },
        {
            "name": "TIME_SERIES_DAILY_ADJUSTED",
            "url": f"https://www.alphavantage.co/query?function=TIME_SERIES_DAILY_ADJUSTED&symbol=AAPL&outputsize=compact&apikey={API_KEY}"
        }
    ]
    
    for test in premium_tests:
        print(f"\n🔍 Testando: {test['name']}")
        try:
            response = requests.get(test['url'])
            data = response.json()
            
            # Verificar se é premium ou se temos dados
            if "premium" in str(data).lower() or "subscription" in str(data).lower():
                print(f"   💰 Endpoint premium: {test['name']}")
            elif "Error Message" in data:
                print(f"   ❌ Erro: {data['Error Message']}")
            elif data and isinstance(data, dict) and len(data) > 0:
                print(f"   ✅ Dados obtidos! Chaves: {list(data.keys())[:3]}...")
                
                # Procurar por bid/ask em qualquer lugar
                data_str = str(data).lower()
                if 'bid' in data_str or 'ask' in data_str:
                    print(f"   🎯 Possível bid/ask encontrado em {test['name']}!")
            else:
                print(f"   ❌ Sem dados")
                
        except Exception as e:
            print(f"   ❌ Erro: {e}")
        
        time.sleep(1)

def test_crypto_endpoints():
    """
    Testa endpoints de criptomoedas que podem ter bid/ask
    """
    print(f"\n₿ TESTANDO ENDPOINTS DE CRIPTOMOEDAS")
    print("=" * 50)
    
    print(f"\n🔍 Testando DIGITAL_CURRENCY_INTRADAY (Bitcoin)")
    try:
        url = f"https://www.alphavantage.co/query?function=DIGITAL_CURRENCY_INTRADAY&symbol=BTC&market=USD&interval=5min&apikey={API_KEY}"
        response = requests.get(url)
        data = response.json()
        
        print(f"Resposta: {list(data.keys()) if isinstance(data, dict) else 'Não é dict'}")
        
        if "Time Series (Digital Currency Intraday)" in data:
            time_series = data["Time Series (Digital Currency Intraday)"]
            latest_time = list(time_series.keys())[0]
            latest_data = time_series[latest_time]
            
            print("✅ Dados crypto obtidos!")
            print(f"   Último horário: {latest_time}")
            print(f"   Campos disponíveis: {list(latest_data.keys())}")
            
            for key, value in latest_data.items():
                print(f"   {key}: {value}")
                if any(term in key.lower() for term in ['bid', 'ask']):
                    print(f"   🎯 ENCONTRADO: {key} = {value}")
        else:
            print(f"❌ Erro ou sem dados: {data}")
            
    except Exception as e:
        print(f"❌ Erro: {e}")

def main():
    """
    Função principal para testes estendidos
    """
    print("🚀 TESTE ESTENDIDO ALPHA VANTAGE - BUSCA AVANÇADA POR BID/ASK")
    print("=" * 70)
    print(f"🔑 Usando API Key: {API_KEY[:8]}...")
    print()
    
    # Teste 1: Forex (mais provável de ter bid/ask)
    test_forex_endpoints()
    
    # Teste 2: Ações brasileiras com formato correto
    test_brazilian_stocks_proper_format()
    
    # Teste 3: Endpoints premium
    test_premium_endpoints()
    
    # Teste 4: Criptomoedas
    test_crypto_endpoints()
    
    # Conclusão final
    print(f"\n\n🎯 CONCLUSÃO FINAL")
    print("=" * 50)
    print("📊 ALPHA VANTAGE - Análise Completa:")
    print("   ✅ Excelente para dados OHLCV tradicionais")
    print("   ✅ Boa cobertura de ações americanas")
    print("   ✅ Dados históricos e intraday confiáveis")
    print("   ✅ Indicadores técnicos integrados")
    print("   ❌ BID/ASK VOLUME não disponível no plano gratuito")
    print("   ❌ Cobertura limitada de ações brasileiras")
    
    print(f"\n💡 RECOMENDAÇÃO:")
    print(f"   Para bid/ask volume, Alpha Vantage não é a melhor opção.")
    print(f"   Continue usando yfinance para dados básicos.")
    print(f"   Para bid/ask volume, considere:")
    print(f"   • Interactive Brokers API (melhor opção)")
    print(f"   • APIs de corretoras brasileiras")
    print(f"   • Polygon.io ou Twelve Data")

if __name__ == "__main__":
    main()
