#!/usr/bin/env python3
"""
Script para gerar gráficos de TODAS as 73 ações brasileiras solicitadas - 12 meses
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import warnings
import os
warnings.filterwarnings('ignore')

# Configurar matplotlib
import matplotlib
matplotlib.use('Agg')  # Backend para salvar sem display

# TODAS as 73 ações brasileiras solicitadas
TODAS_ACOES = [
    ('ABEV3.SA', 'AMBEV S/A'),
    ('AZUL4.SA', 'AZUL'),
    ('B3SA3.SA', 'B3'),
    ('BBAS3.SA', 'BRASIL'),
    ('BBDC3.SA', 'BRADESCO'),
    ('BBDC4.SA', 'BRADESCO'),
    ('BBSE3.SA', 'BBSEGURIDADE'),
    ('BEEF3.SA', 'MINERVA'),
    ('BPAC11.SA', 'BTGP BANCO'),
    ('BRAP4.SA', 'BRADESPAR'),
    ('BRDT3.SA', 'PETROBRAS BR'),
    ('BRFS3.SA', 'BRF SA'),
    ('BRKM5.SA', 'BRASKEM'),
    ('BRML3.SA', 'BR MALLS PAR'),
    ('BTOW3.SA', 'B2W DIGITAL'),
    ('CCRO3.SA', 'CCR SA'),
    ('CIEL3.SA', 'CIELO'),
    ('CMIG4.SA', 'CEMIG'),
    ('COGN3.SA', 'COGNA ON'),
    ('CPFE3.SA', 'CPFL ENERGIA'),
    ('CRFB3.SA', 'CARREFOUR BR'),
    ('CSAN3.SA', 'COSAN'),
    ('CSNA3.SA', 'SID NACIONAL'),
    ('CVCB3.SA', 'CVC BRASIL'),
    ('CYRE3.SA', 'CYRELA REALT'),
    ('ECOR3.SA', 'ECORODOVIAS'),
    ('EGIE3.SA', 'ENGIE BRASIL'),
    ('ELET3.SA', 'ELETROBRAS'),
    ('ELET6.SA', 'ELETROBRAS'),
    ('EMBR3.SA', 'EMBRAER'),
    ('ENBR3.SA', 'ENERGIAS BR'),
    ('ENGI11.SA', 'ENERGISA'),
    ('EQTL3.SA', 'EQUATORIAL'),
    ('EZTC3.SA', 'EZTEC'),
    ('FLRY3.SA', 'FLEURY'),
    ('GGBR4.SA', 'GERDAU'),
    ('GNDI3.SA', 'INTERMEDICA'),
    ('GOAU4.SA', 'GERDAU MET'),
    ('GOLL4.SA', 'GOL'),
    ('HAPV3.SA', 'HAPVIDA'),
    ('HGTX3.SA', 'CIA HERING'),
    ('HYPE3.SA', 'HYPERA'),
    ('IGTA3.SA', 'IGUATEMI'),
    ('IRBR3.SA', 'IRBBRASIL RE'),
    ('ITSA4.SA', 'ITAUSA'),
    ('ITUB4.SA', 'ITAUUNIBANCO'),
    ('JBSS3.SA', 'JBS'),
    ('KLBN11.SA', 'KLABIN S/A'),
    ('LAME4.SA', 'LOJAS AMERIC'),
    ('LREN3.SA', 'LOJAS RENNER'),
    ('MGLU3.SA', 'MAGAZ LUIZA'),
    ('MRFG3.SA', 'MARFRIG'),
    ('MRVE3.SA', 'MRV'),
    ('MULT3.SA', 'MULTIPLAN'),
    ('NTCO3.SA', 'GRUPO NATURA'),
    ('PCAR3.SA', 'P.ACUCAR-CBD'),
    ('PETR3.SA', 'PETROBRAS'),
    ('PETR4.SA', 'PETROBRAS'),
    ('PRIO3.SA', 'PETRORIO'),
    ('QUAL3.SA', 'QUALICORP'),
    ('RADL3.SA', 'RAIADROGASIL'),
    ('RAIL3.SA', 'RUMO S.A.'),
    ('RENT3.SA', 'LOCALIZA'),
    ('SANB11.SA', 'SANTANDER BR'),
    ('SBSP3.SA', 'SABESP'),
    ('SULA11.SA', 'SUL AMERICA'),
    ('SUZB3.SA', 'SUZANO S.A.'),
    ('TAEE11.SA', 'TAESA'),
    ('TIMS3.SA', 'TIM'),
    ('TOTS3.SA', 'TOTVS'),
    ('UGPA3.SA', 'ULTRAPAR'),
    ('USIM5.SA', 'USIMINAS'),
    ('VALE3.SA', 'VALE'),
    ('VIVT4.SA', 'TELEF BRASIL'),
    ('VVAR3.SA', 'VIAVAREJO'),
    ('WEGE3.SA', 'WEG'),
    ('YDUQ3.SA', 'YDUQS PART')
]

def obter_dados(ticker, nome):
    """Obtém dados de 12 meses"""
    try:
        print(f"  📊 {ticker.replace('.SA', ''):8s} - {nome}")
        stock = yf.Ticker(ticker)
        dados = stock.history(period="1y")
        
        if dados.empty:
            print(f"     ⚠️ Sem dados")
            return None
            
        print(f"     ✅ {len(dados)} dias")
        return dados
        
    except Exception as e:
        print(f"     ❌ Erro: {str(e)[:50]}")
        return None

def criar_grafico(ticker, nome, dados):
    """Cria gráfico individual"""
    plt.figure(figsize=(14, 10))
    
    # Subplot 1: Preço
    plt.subplot(2, 1, 1)
    plt.plot(dados.index, dados['Close'], linewidth=2, color='blue')
    plt.fill_between(dados.index, dados['Low'], dados['High'], alpha=0.2, color='lightblue')
    
    # Calcular estatísticas
    preco_inicial = dados['Close'].iloc[0]
    preco_final = dados['Close'].iloc[-1]
    performance = ((preco_final / preco_inicial) - 1) * 100
    
    plt.title(f'{nome} ({ticker.replace(".SA", "")}) - 12 Meses', fontsize=16, fontweight='bold')
    plt.ylabel('Preço (R$)')
    plt.grid(True, alpha=0.3)
    
    # Adicionar estatísticas
    cor = 'green' if performance >= 0 else 'red'
    stats = f'Inicial: R$ {preco_inicial:.2f} | Atual: R$ {preco_final:.2f} | Performance: {performance:+.1f}%'
    plt.text(0.02, 0.98, stats, transform=plt.gca().transAxes, 
             fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='white', alpha=0.9, edgecolor=cor))
    
    # Subplot 2: Volume
    plt.subplot(2, 1, 2)
    plt.bar(dados.index, dados['Volume']/1e6, alpha=0.7, color='orange', width=0.8)
    plt.title('Volume (Milhões)')
    plt.xlabel('Data')
    plt.ylabel('Volume (Mi)')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()

    # Ensure directory exists
    import os
    os.makedirs('../results/figures/individual_stocks', exist_ok=True)

    # Salvar
    nome_arquivo = f"../results/figures/individual_stocks/acao_{ticker.replace('.SA', '')}_12m.png"
    plt.savefig(nome_arquivo, dpi=200, bbox_inches='tight')
    plt.close()

    print(f"     💾 {nome_arquivo}")
    
    return {
        'ticker': ticker,
        'nome': nome,
        'performance': performance,
        'preco_inicial': preco_inicial,
        'preco_final': preco_final
    }

def criar_resumo(resultados):
    """Cria gráfico resumo e relatório"""
    print("\n📊 Criando resumo...")
    
    # Ordenar por performance
    resultados_ord = sorted(resultados, key=lambda x: x['performance'], reverse=True)
    
    # Gráfico de barras
    plt.figure(figsize=(20, 12))
    
    tickers = [r['ticker'].replace('.SA', '') for r in resultados_ord]
    performances = [r['performance'] for r in resultados_ord]
    
    cores = ['green' if p >= 0 else 'red' for p in performances]
    
    plt.bar(range(len(tickers)), performances, color=cores, alpha=0.7)
    plt.title('Performance de Todas as Ações Brasileiras - 12 Meses', fontsize=18, fontweight='bold')
    plt.xlabel('Ações')
    plt.ylabel('Performance (%)')
    plt.xticks(range(len(tickers)), tickers, rotation=90, fontsize=8)
    plt.grid(True, alpha=0.3, axis='y')
    plt.axhline(y=0, color='black', linestyle='-', alpha=0.8)
    
    plt.tight_layout()

    # Ensure directory exists
    import os
    os.makedirs('../results/figures/dashboards', exist_ok=True)

    plt.savefig('../results/figures/dashboards/resumo_todas_acoes_73.png', dpi=200, bbox_inches='tight')
    plt.close()

    print("   💾 ../results/figures/dashboards/resumo_todas_acoes_73.png")
    
    # Relatório
    print("\n" + "="*90)
    print("📈 RELATÓRIO FINAL - TODAS AS 73 AÇÕES BRASILEIRAS")
    print("="*90)
    
    print(f"{'#':<3} {'Ticker':<8} {'Nome':<25} {'Performance':<12} {'Preço Atual':<12}")
    print("-" * 90)
    
    for i, r in enumerate(resultados_ord, 1):
        emoji = "🟢" if r['performance'] >= 0 else "🔴"
        print(f"{i:<3} {emoji} {r['ticker'].replace('.SA', ''):<6} "
              f"{r['nome'][:24]:<25} {r['performance']:>+8.1f}% "
              f"R$ {r['preco_final']:>8.2f}")
    
    # Estatísticas
    performances = [r['performance'] for r in resultados]
    positivas = len([p for p in performances if p >= 0])
    
    print("\n" + "="*90)
    print("📊 ESTATÍSTICAS")
    print("="*90)
    print(f"Total analisadas: {len(resultados)}")
    print(f"Performance positiva: {positivas} ({positivas/len(resultados)*100:.1f}%)")
    print(f"Performance média: {np.mean(performances):.1f}%")
    print(f"Melhor: {max(performances):.1f}%")
    print(f"Pior: {min(performances):.1f}%")

def main():
    print("🚀 TODAS AS 73 AÇÕES BRASILEIRAS - GRÁFICOS 12 MESES")
    print("="*70)
    print(f"📋 Total: {len(TODAS_ACOES)} ações")
    print("📊 Será gerado para cada ação:")
    print("   • Gráfico individual (preço + volume)")
    print("   • Estatísticas de performance")
    print("📊 Ao final:")
    print("   • Gráfico resumo comparativo")
    print("   • Relatório completo")
    
    print(f"\n⚠️  ATENÇÃO: Serão gerados {len(TODAS_ACOES)} arquivos PNG!")
    print("⏱️  Tempo estimado: 5-10 minutos")
    
    confirma = input("\nDeseja continuar? (s/N): ").strip().lower()
    if confirma != 's':
        print("❌ Cancelado pelo usuário")
        return
    
    print(f"\n📈 Iniciando análise de {len(TODAS_ACOES)} ações...")
    
    resultados = []
    sucesso = 0
    erro = 0
    
    for i, (ticker, nome) in enumerate(TODAS_ACOES, 1):
        print(f"\n[{i:2d}/{len(TODAS_ACOES)}]", end=" ")
        
        dados = obter_dados(ticker, nome)
        
        if dados is not None:
            resultado = criar_grafico(ticker, nome, dados)
            resultados.append(resultado)
            sucesso += 1
        else:
            erro += 1
    
    print(f"\n✅ Processamento concluído!")
    print(f"   Sucessos: {sucesso}")
    print(f"   Erros: {erro}")
    
    if resultados:
        criar_resumo(resultados)
        
        print(f"\n📁 ARQUIVOS GERADOS: {sucesso + 1} arquivos PNG")
        print("   • 1 gráfico resumo comparativo")
        print(f"   • {sucesso} gráficos individuais")
        
    else:
        print("\n❌ Nenhum gráfico foi gerado!")

if __name__ == "__main__":
    main()
