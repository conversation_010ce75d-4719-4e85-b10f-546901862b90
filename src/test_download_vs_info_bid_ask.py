#!/usr/bin/env python3
"""
Teste comparativo: yfinance.download() vs ticker.info() para dados de bid/ask
Verificando se é possível obter dados históricos de bid/ask com .download()
"""

import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def test_download_method():
    """
    Testa o método yf.download() para ver quais colunas estão disponíveis
    """
    print("🔍 TESTE 1: Método yf.download()")
    print("=" * 60)
    
    # Testar com ação brasileira
    ticker_symbol = "PETR4.SA"
    
    try:
        # Download de 18 meses de dados
        print(f"📊 Baixando 18 meses de dados para {ticker_symbol}...")
        data = yf.download(ticker_symbol, period="18mo", interval="1d")
        
        print(f"✅ Dados obtidos: {len(data)} dias")
        print(f"📅 Período: {data.index[0].date()} até {data.index[-1].date()}")
        print(f"📋 Colunas disponíveis: {list(data.columns)}")
        
        # Verificar se há colunas relacionadas a bid/ask
        bid_ask_columns = []
        for col in data.columns:
            col_str = str(col).lower()
            if any(term in col_str for term in ['bid', 'ask', 'spread']):
                bid_ask_columns.append(col)
        
        if bid_ask_columns:
            print(f"🎯 Colunas bid/ask encontradas: {bid_ask_columns}")
            for col in bid_ask_columns:
                print(f"   {col}: {data[col].dropna().tail(5).tolist()}")
        else:
            print("❌ Nenhuma coluna bid/ask encontrada no download")
        
        # Mostrar amostra dos dados
        print(f"\n📊 Amostra dos últimos 5 dias:")
        print(data.tail().round(2))
        
        return data
        
    except Exception as e:
        print(f"❌ Erro no download: {e}")
        return None

def test_info_method():
    """
    Testa o método ticker.info() para dados de bid/ask
    """
    print("\n🔍 TESTE 2: Método ticker.info()")
    print("=" * 60)
    
    ticker_symbol = "PETR4.SA"
    
    try:
        ticker = yf.Ticker(ticker_symbol)
        info = ticker.info
        
        print(f"📊 Dados de info para {ticker_symbol}")
        
        # Buscar campos bid/ask
        bid_ask_fields = {}
        for key, value in info.items():
            key_lower = key.lower()
            if any(term in key_lower for term in ['bid', 'ask', 'spread']):
                bid_ask_fields[key] = value
        
        if bid_ask_fields:
            print(f"🎯 Campos bid/ask encontrados:")
            for key, value in bid_ask_fields.items():
                print(f"   {key}: {value}")
                
            # Calcular spread se possível
            if 'bid' in bid_ask_fields and 'ask' in bid_ask_fields:
                bid = bid_ask_fields['bid']
                ask = bid_ask_fields['ask']
                if bid and ask and bid > 0:
                    spread = ask - bid
                    spread_pct = (spread / bid) * 100
                    print(f"   📈 Spread calculado: R$ {spread:.4f} ({spread_pct:.4f}%)")
        else:
            print("❌ Nenhum campo bid/ask encontrado no info")
        
        return bid_ask_fields
        
    except Exception as e:
        print(f"❌ Erro no info: {e}")
        return None

def test_history_method():
    """
    Testa o método ticker.history() para ver se há diferenças
    """
    print("\n🔍 TESTE 3: Método ticker.history()")
    print("=" * 60)
    
    ticker_symbol = "PETR4.SA"
    
    try:
        ticker = yf.Ticker(ticker_symbol)
        
        # Obter 18 meses de dados históricos
        print(f"📊 Obtendo histórico de 18 meses para {ticker_symbol}...")
        history = ticker.history(period="18mo", interval="1d")
        
        print(f"✅ Dados obtidos: {len(history)} dias")
        print(f"📅 Período: {history.index[0].date()} até {history.index[-1].date()}")
        print(f"📋 Colunas disponíveis: {list(history.columns)}")
        
        # Verificar se há colunas relacionadas a bid/ask
        bid_ask_columns = []
        for col in history.columns:
            col_str = str(col).lower()
            if any(term in col_str for term in ['bid', 'ask', 'spread']):
                bid_ask_columns.append(col)
        
        if bid_ask_columns:
            print(f"🎯 Colunas bid/ask encontradas: {bid_ask_columns}")
        else:
            print("❌ Nenhuma coluna bid/ask encontrada no history")
        
        # Mostrar amostra dos dados
        print(f"\n📊 Amostra dos últimos 5 dias:")
        print(history.tail().round(2))
        
        return history
        
    except Exception as e:
        print(f"❌ Erro no history: {e}")
        return None

def test_multiple_intervals():
    """
    Testa diferentes intervalos para ver se bid/ask aparecem
    """
    print("\n🔍 TESTE 4: Diferentes Intervalos")
    print("=" * 60)
    
    ticker_symbol = "PETR4.SA"
    intervals = ["1m", "5m", "15m", "1h", "1d"]
    
    for interval in intervals:
        try:
            print(f"\n📊 Testando intervalo: {interval}")
            
            # Ajustar período baseado no intervalo
            if interval in ["1m", "5m"]:
                period = "1d"  # Máximo 1 dia para intervalos de minutos
            elif interval == "15m":
                period = "5d"  # Máximo 5 dias para 15min
            elif interval == "1h":
                period = "1mo"  # 1 mês para 1h
            else:
                period = "18mo"  # 18 meses para 1d
            
            data = yf.download(ticker_symbol, period=period, interval=interval)
            
            if not data.empty:
                print(f"   ✅ {len(data)} registros obtidos")
                print(f"   📋 Colunas: {list(data.columns)}")
                
                # Verificar bid/ask
                bid_ask_cols = [col for col in data.columns 
                               if any(term in str(col).lower() for term in ['bid', 'ask', 'spread'])]
                
                if bid_ask_cols:
                    print(f"   🎯 Bid/Ask encontrado: {bid_ask_cols}")
                else:
                    print(f"   ❌ Sem bid/ask")
            else:
                print(f"   ❌ Sem dados")
                
        except Exception as e:
            print(f"   ❌ Erro: {e}")

def main():
    """
    Executa todos os testes comparativos
    """
    print("🚀 TESTE COMPARATIVO: download() vs info() para Bid/Ask")
    print("=" * 80)
    print("Objetivo: Verificar se é possível obter 18 meses de dados bid/ask históricos")
    print("=" * 80)
    
    # Teste 1: yf.download()
    download_data = test_download_method()
    
    # Teste 2: ticker.info()
    info_data = test_info_method()
    
    # Teste 3: ticker.history()
    history_data = test_history_method()
    
    # Teste 4: Diferentes intervalos
    test_multiple_intervals()
    
    # Conclusões
    print("\n" + "=" * 80)
    print("📋 CONCLUSÕES")
    print("=" * 80)
    
    print("\n1️⃣ MÉTODO yf.download():")
    if download_data is not None:
        has_bid_ask = any('bid' in str(col).lower() or 'ask' in str(col).lower() 
                         for col in download_data.columns)
        if has_bid_ask:
            print("   ✅ Contém dados de bid/ask históricos")
        else:
            print("   ❌ NÃO contém dados de bid/ask históricos")
            print("   📊 Apenas dados OHLCV (Open, High, Low, Close, Volume)")
    else:
        print("   ❌ Falhou ao obter dados")
    
    print("\n2️⃣ MÉTODO ticker.info():")
    if info_data:
        print("   ✅ Contém dados de bid/ask ATUAIS (snapshot)")
        print("   ⚠️ Apenas dados do momento presente, não históricos")
    else:
        print("   ❌ Não contém dados de bid/ask")
    
    print("\n3️⃣ RESPOSTA À PERGUNTA:")
    print("   ❌ NÃO é possível obter 18 meses de dados bid/ask históricos")
    print("   📊 yf.download() e ticker.history() fornecem apenas OHLCV")
    print("   💡 ticker.info() fornece bid/ask atual, mas não histórico")
    
    print("\n4️⃣ ALTERNATIVAS:")
    print("   🔄 Coletar dados bid/ask em tempo real e armazenar")
    print("   💰 Usar APIs pagas (Alpha Vantage, Twelve Data, etc.)")
    print("   📈 Usar spread estimado baseado em volatilidade")
    print("   🏦 Acessar dados diretamente da corretora/exchange")

if __name__ == "__main__":
    main()
