#!/usr/bin/env python3
"""
Análise de Correlação entre Ações Brasileiras
- Mapa de calor de correlações
- Identificação de ações com baixa correlação para diversificação
- Exportação para CSV
"""

import yfinance as yf
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Configurar matplotlib
import matplotlib
matplotlib.use('Agg')
plt.style.use('default')

# Ações brasileiras para análise (principais + diversificadas)
ACOES_ANALISE = [
    'PETR4.SA', 'PETR3.SA', 'VALE3.SA', 'ITUB4.SA', 'BBDC4.SA', 'ABEV3.SA',
    'WEGE3.SA', 'RENT3.SA', 'LREN3.SA', 'MGLU3.SA', 'SUZB3.SA', 'JBSS3.SA',
    'B3SA3.SA', 'RAIL3.SA', 'CCRO3.SA', 'ELET3.SA', 'BBAS3.SA', 'RADL3.SA',
    'CSAN3.SA', 'GGBR4.SA', 'EMBR3.SA', 'CPFE3.SA', 'EQTL3.SA', 'KLBN11.SA',
    'TOTS3.SA', 'HAPV3.SA', 'GNDI3.SA', 'FLRY3.SA', 'MULT3.SA', 'PCAR3.SA',
    'SANB11.SA', 'SBSP3.SA', 'TAEE11.SA', 'TIMS3.SA', 'UGPA3.SA', 'VIVT4.SA',
    'AZUL4.SA', 'GOLL4.SA', 'BRFS3.SA', 'MRFG3.SA', 'BEEF3.SA', 'COGN3.SA',
    'YDUQ3.SA', 'CYRE3.SA', 'MRVE3.SA', 'EZTC3.SA', 'HYPE3.SA', 'QUAL3.SA'
]

# Nomes das empresas
NOMES_EMPRESAS = {
    'PETR4.SA': 'Petrobras PN', 'PETR3.SA': 'Petrobras ON', 'VALE3.SA': 'Vale',
    'ITUB4.SA': 'Itaú', 'BBDC4.SA': 'Bradesco', 'ABEV3.SA': 'Ambev',
    'WEGE3.SA': 'WEG', 'RENT3.SA': 'Localiza', 'LREN3.SA': 'Lojas Renner',
    'MGLU3.SA': 'Magazine Luiza', 'SUZB3.SA': 'Suzano', 'JBSS3.SA': 'JBS',
    'B3SA3.SA': 'B3', 'RAIL3.SA': 'Rumo', 'CCRO3.SA': 'CCR',
    'ELET3.SA': 'Eletrobras', 'BBAS3.SA': 'Banco do Brasil', 'RADL3.SA': 'Raia Drogasil',
    'CSAN3.SA': 'Cosan', 'GGBR4.SA': 'Gerdau', 'EMBR3.SA': 'Embraer',
    'CPFE3.SA': 'CPFL Energia', 'EQTL3.SA': 'Equatorial', 'KLBN11.SA': 'Klabin',
    'TOTS3.SA': 'TOTVS', 'HAPV3.SA': 'Hapvida', 'GNDI3.SA': 'NotreDame',
    'FLRY3.SA': 'Fleury', 'MULT3.SA': 'Multiplan', 'PCAR3.SA': 'Pão de Açúcar',
    'SANB11.SA': 'Santander', 'SBSP3.SA': 'Sabesp', 'TAEE11.SA': 'Taesa',
    'TIMS3.SA': 'TIM', 'UGPA3.SA': 'Ultrapar', 'VIVT4.SA': 'Telefônica',
    'AZUL4.SA': 'Azul', 'GOLL4.SA': 'Gol', 'BRFS3.SA': 'BRF',
    'MRFG3.SA': 'Marfrig', 'BEEF3.SA': 'Minerva', 'COGN3.SA': 'Cogna',
    'YDUQ3.SA': 'Yduqs', 'CYRE3.SA': 'Cyrela', 'MRVE3.SA': 'MRV',
    'EZTC3.SA': 'Eztec', 'HYPE3.SA': 'Hypera', 'QUAL3.SA': 'Qualicorp'
}

def obter_dados_retornos(acoes, periodo="1y"):
    """Obtém dados de retornos diários das ações"""
    print(f"📊 Obtendo dados de {len(acoes)} ações para análise de correlação...")
    
    dados_precos = pd.DataFrame()
    acoes_validas = []
    
    for i, ticker in enumerate(acoes, 1):
        try:
            print(f"  [{i:2d}/{len(acoes)}] {ticker.replace('.SA', ''):8s}", end="")
            
            stock = yf.Ticker(ticker)
            hist = stock.history(period=periodo)
            
            if len(hist) < 200:  # Mínimo de dados
                print(" ❌ Dados insuficientes")
                continue
                
            dados_precos[ticker] = hist['Close']
            acoes_validas.append(ticker)
            print(" ✅")
            
        except Exception as e:
            print(f" ❌ Erro")
            continue
    
    if dados_precos.empty:
        return None, []
    
    # Calcular retornos diários
    retornos = dados_precos.pct_change().dropna()
    
    print(f"\n✅ Dados obtidos para {len(acoes_validas)} ações")
    print(f"📅 Período: {retornos.index[0].strftime('%Y-%m-%d')} a {retornos.index[-1].strftime('%Y-%m-%d')}")
    print(f"📊 Total de {len(retornos)} dias de dados")
    
    return retornos, acoes_validas

def criar_mapa_calor_correlacao(retornos, acoes_validas):
    """Cria mapa de calor das correlações"""
    print("\n🔥 Criando mapa de calor de correlações...")
    
    # Calcular matriz de correlação
    correlacao = retornos.corr()
    
    # Criar figura
    plt.figure(figsize=(20, 16))
    
    # Criar labels mais limpos
    labels = [ticker.replace('.SA', '') for ticker in acoes_validas]
    
    # Mapa de calor
    mask = np.triu(np.ones_like(correlacao, dtype=bool))  # Mascarar triângulo superior
    
    sns.heatmap(correlacao, 
                mask=mask,
                annot=True, 
                fmt='.2f',
                cmap='RdYlBu_r',
                center=0,
                square=True,
                xticklabels=labels,
                yticklabels=labels,
                cbar_kws={"shrink": .8},
                annot_kws={'size': 7})
    
    plt.title('Mapa de Calor - Correlação entre Ações Brasileiras\n(Retornos Diários - 12 Meses)', 
              fontsize=18, fontweight='bold', pad=20)
    plt.xlabel('Ações', fontsize=12)
    plt.ylabel('Ações', fontsize=12)
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    
    plt.tight_layout()

    # Ensure directory exists
    import os
    os.makedirs('results/figures/correlation_analysis', exist_ok=True)

    plt.savefig('results/figures/correlation_analysis/mapa_calor_correlacao.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("   💾 results/figures/correlation_analysis/mapa_calor_correlacao.png")
    
    return correlacao

def analisar_correlacoes(correlacao, acoes_validas, threshold_baixa=0.3):
    """Analisa as correlações e identifica pares com baixa correlação"""
    print(f"\n🔍 Analisando correlações (threshold baixa correlação: ≤{threshold_baixa})...")
    
    # Estatísticas gerais
    correlacoes_valores = []
    for i in range(len(correlacao)):
        for j in range(i+1, len(correlacao)):
            correlacoes_valores.append(correlacao.iloc[i, j])
    
    media_corr = np.mean(correlacoes_valores)
    mediana_corr = np.median(correlacoes_valores)
    std_corr = np.std(correlacoes_valores)
    
    print(f"📊 Estatísticas das correlações:")
    print(f"   Média: {media_corr:.3f}")
    print(f"   Mediana: {mediana_corr:.3f}")
    print(f"   Desvio padrão: {std_corr:.3f}")
    print(f"   Mínima: {min(correlacoes_valores):.3f}")
    print(f"   Máxima: {max(correlacoes_valores):.3f}")
    
    # Encontrar pares com baixa correlação
    pares_baixa_correlacao = []
    
    for i in range(len(correlacao)):
        for j in range(i+1, len(correlacao)):
            corr_valor = correlacao.iloc[i, j]
            if abs(corr_valor) <= threshold_baixa:
                ticker1 = acoes_validas[i]
                ticker2 = acoes_validas[j]
                nome1 = NOMES_EMPRESAS.get(ticker1, ticker1.replace('.SA', ''))
                nome2 = NOMES_EMPRESAS.get(ticker2, ticker2.replace('.SA', ''))
                
                pares_baixa_correlacao.append({
                    'Acao1': ticker1.replace('.SA', ''),
                    'Nome1': nome1,
                    'Acao2': ticker2.replace('.SA', ''),
                    'Nome2': nome2,
                    'Correlacao': corr_valor,
                    'Correlacao_Abs': abs(corr_valor)
                })
    
    # Ordenar por correlação absoluta (menor primeiro)
    pares_baixa_correlacao.sort(key=lambda x: x['Correlacao_Abs'])
    
    print(f"\n🎯 Encontrados {len(pares_baixa_correlacao)} pares com correlação ≤ {threshold_baixa}")
    
    return pares_baixa_correlacao, {
        'media': media_corr,
        'mediana': mediana_corr,
        'std': std_corr,
        'min': min(correlacoes_valores),
        'max': max(correlacoes_valores)
    }

def identificar_acoes_diversificacao(pares_baixa_correlacao, correlacao_matriz, acoes_validas, top_n=20, threshold_alta_correlacao=0.9):
    """Identifica as melhores ações para diversificação, eliminando ações com alta correlação entre si"""
    print(f"\n🎯 Identificando top {top_n} ações para diversificação...")
    print(f"   Eliminando ações com correlação > {threshold_alta_correlacao} entre si...")

    # Contar quantas vezes cada ação aparece em pares de baixa correlação
    contador_acoes = {}

    for par in pares_baixa_correlacao:
        acao1 = par['Acao1']
        acao2 = par['Acao2']

        contador_acoes[acao1] = contador_acoes.get(acao1, 0) + 1
        contador_acoes[acao2] = contador_acoes.get(acao2, 0) + 1

    # Ordenar por frequência (candidatos)
    acoes_candidatas = sorted(contador_acoes.items(), key=lambda x: x[1], reverse=True)

    # FASE 1: Seleção inicial de 20 ações
    acoes_selecionadas = []
    acoes_eliminadas = []

    print(f"\n📋 FASE 1 - Seleção inicial de {top_n} ações:")

    for acao_candidata, freq in acoes_candidatas:
        if len(acoes_selecionadas) >= top_n:
            break

        ticker_candidato = f"{acao_candidata}.SA"
        ticker_completo = f"{acao_candidata}.SA"
        nome = NOMES_EMPRESAS.get(ticker_completo, acao_candidata)

        acoes_selecionadas.append({
            'Ticker': acao_candidata,
            'Nome': nome,
            'Frequencia_Baixa_Correlacao': freq,
            'Percentual_Pares': (freq / len(pares_baixa_correlacao)) * 100 if pares_baixa_correlacao else 0
        })
        print(f"   ✅ {acao_candidata:8s} - Selecionada (freq: {freq})")

    print(f"\n📊 Resultado da Fase 1: {len(acoes_selecionadas)} ações selecionadas")

    # FASE 2: Eliminação de ações com alta correlação entre si
    print(f"\n📋 FASE 2 - Eliminando ações com correlação > {threshold_alta_correlacao}:")

    acoes_finais = []
    acoes_removidas = []

    for acao in acoes_selecionadas:
        ticker_atual = f"{acao['Ticker']}.SA"

        # Verificar se a ação atual tem alta correlação com alguma já aprovada
        tem_alta_correlacao = False
        acao_conflitante = None
        correlacao_conflito = 0

        for acao_aprovada in acoes_finais:
            ticker_aprovado = f"{acao_aprovada['Ticker']}.SA"

            try:
                idx_atual = acoes_validas.index(ticker_atual)
                idx_aprovado = acoes_validas.index(ticker_aprovado)

                correlacao_valor = abs(correlacao_matriz.iloc[idx_atual, idx_aprovado])

                if correlacao_valor > threshold_alta_correlacao:
                    tem_alta_correlacao = True
                    acao_conflitante = acao_aprovada['Ticker']
                    correlacao_conflito = correlacao_valor
                    break

            except ValueError:
                continue

        if tem_alta_correlacao:
            print(f"   ❌ {acao['Ticker']:8s} - Alta correlação ({correlacao_conflito:.3f}) com {acao_conflitante}")
            acoes_removidas.append({
                'Ticker': acao['Ticker'],
                'Nome': acao['Nome'],
                'Motivo': f'Alta correlação ({correlacao_conflito:.3f}) com {acao_conflitante}',
                'Frequencia': acao['Frequencia_Baixa_Correlacao']
            })
        else:
            acoes_finais.append(acao)
            print(f"   ✅ {acao['Ticker']:8s} - Aprovada")

    print(f"\n📊 Resultado da Fase 2:")
    print(f"   Ações aprovadas: {len(acoes_finais)}")
    print(f"   Ações removidas por alta correlação: {len(acoes_removidas)}")

    # FASE 3: Backfill para completar 20 ações se necessário
    if len(acoes_finais) < top_n:
        print(f"\n📋 FASE 3 - Backfill para completar {top_n} ações:")
        print(f"   Faltam {top_n - len(acoes_finais)} ações")

        # Criar lista de ações restantes (não selecionadas inicialmente)
        acoes_ja_consideradas = {acao['Ticker'] for acao in acoes_selecionadas}
        acoes_restantes = []

        for acao_candidata, freq in acoes_candidatas:
            if acao_candidata not in acoes_ja_consideradas:
                acoes_restantes.append((acao_candidata, freq))

        # Tentar adicionar ações restantes
        for acao_candidata, freq in acoes_restantes:
            if len(acoes_finais) >= top_n:
                break

            ticker_candidato = f"{acao_candidata}.SA"

            # Verificar se tem alta correlação com as já aprovadas
            tem_alta_correlacao = False
            acao_conflitante = None
            correlacao_conflito = 0

            for acao_aprovada in acoes_finais:
                ticker_aprovado = f"{acao_aprovada['Ticker']}.SA"

                try:
                    idx_candidato = acoes_validas.index(ticker_candidato)
                    idx_aprovado = acoes_validas.index(ticker_aprovado)

                    correlacao_valor = abs(correlacao_matriz.iloc[idx_candidato, idx_aprovado])

                    if correlacao_valor > threshold_alta_correlacao:
                        tem_alta_correlacao = True
                        acao_conflitante = acao_aprovada['Ticker']
                        correlacao_conflito = correlacao_valor
                        break

                except ValueError:
                    continue

            if not tem_alta_correlacao:
                ticker_completo = f"{acao_candidata}.SA"
                nome = NOMES_EMPRESAS.get(ticker_completo, acao_candidata)

                acoes_finais.append({
                    'Ticker': acao_candidata,
                    'Nome': nome,
                    'Frequencia_Baixa_Correlacao': freq,
                    'Percentual_Pares': (freq / len(pares_baixa_correlacao)) * 100 if pares_baixa_correlacao else 0
                })
                print(f"   ✅ {acao_candidata:8s} - Adicionada no backfill (freq: {freq})")
            else:
                print(f"   ❌ {acao_candidata:8s} - Alta correlação ({correlacao_conflito:.3f}) com {acao_conflitante}")
                acoes_eliminadas.append({
                    'Ticker': acao_candidata,
                    'Motivo': f'Alta correlação ({correlacao_conflito:.3f}) com {acao_conflitante}',
                    'Frequencia': freq
                })

        print(f"\n📊 Resultado da Fase 3:")
        print(f"   Total de ações finais: {len(acoes_finais)}")

    # Consolidar ações eliminadas
    acoes_eliminadas.extend(acoes_removidas)

    print(f"\n📊 RESULTADO FINAL:")
    print(f"   Ações selecionadas: {len(acoes_finais)}")
    print(f"   Ações eliminadas por alta correlação: {len(acoes_eliminadas)}")

    if acoes_eliminadas:
        print(f"\n🚫 Ações eliminadas:")
        for acao in acoes_eliminadas[:10]:  # Mostrar apenas as 10 primeiras
            print(f"   • {acao['Ticker']:8s} - {acao['Motivo']}")
        if len(acoes_eliminadas) > 10:
            print(f"   ... e mais {len(acoes_eliminadas) - 10} ações")

    return acoes_finais, acoes_eliminadas

def salvar_resultados_csv(pares_baixa_correlacao, melhores_diversificacao, acoes_eliminadas, stats_correlacao):
    """Salva resultados em arquivos CSV"""
    print("\n💾 Salvando resultados em CSV...")

    # Ensure directory exists
    import os
    os.makedirs('results/csv/correlation_data', exist_ok=True)

    # 1. Pares com baixa correlação
    df_pares = pd.DataFrame(pares_baixa_correlacao)
    df_pares.to_csv('results/csv/correlation_data/pares_baixa_correlacao.csv', index=False, encoding='utf-8')
    print("   📄 results/csv/correlation_data/pares_baixa_correlacao.csv")

    # 2. Melhores ações para diversificação
    df_diversificacao = pd.DataFrame(melhores_diversificacao)
    df_diversificacao.to_csv('results/csv/correlation_data/acoes_diversificacao.csv', index=False, encoding='utf-8')
    print("   📄 results/csv/correlation_data/acoes_diversificacao.csv")

    # 3. Ações eliminadas por alta correlação
    if acoes_eliminadas:
        df_eliminadas = pd.DataFrame(acoes_eliminadas)
        df_eliminadas.to_csv('results/csv/correlation_data/acoes_eliminadas_alta_correlacao.csv', index=False, encoding='utf-8')
        print("   📄 results/csv/correlation_data/acoes_eliminadas_alta_correlacao.csv")

    # 4. Estatísticas gerais
    df_stats = pd.DataFrame([stats_correlacao])
    df_stats.to_csv('results/csv/correlation_data/estatisticas_correlacao.csv', index=False, encoding='utf-8')
    print("   📄 results/csv/correlation_data/estatisticas_correlacao.csv")



def criar_graficos_analise(pares_baixa_correlacao, melhores_diversificacao, stats_correlacao):
    """Cria gráficos adicionais de análise"""
    print("\n📊 Criando gráficos de análise...")
    
    _, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))
    
    # Gráfico 1: Distribuição das correlações
    correlacoes = [par['Correlacao'] for par in pares_baixa_correlacao]
    
    if correlacoes:
        ax1.hist(correlacoes, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.axvline(stats_correlacao['media'], color='red', linestyle='--', 
                    label=f'Média Geral: {stats_correlacao["media"]:.3f}')
        ax1.set_title('Distribuição das Correlações\n(Pares com Baixa Correlação ≤0.3)', fontweight='bold')
        ax1.set_xlabel('Correlação')
        ax1.set_ylabel('Frequência')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
    
    # Gráfico 2: Top 15 ações para diversificação
    top_15 = melhores_diversificacao[:15]
    tickers = [item['Ticker'] for item in top_15]
    frequencias = [item['Frequencia_Baixa_Correlacao'] for item in top_15]
    
    bars = ax2.bar(range(len(tickers)), frequencias, color='lightgreen', alpha=0.7)
    ax2.set_title('TOP 15 - Ações para Diversificação', fontweight='bold')
    ax2.set_xlabel('Ações')
    ax2.set_ylabel('Frequência em Pares de Baixa Correlação')
    ax2.set_xticks(range(len(tickers)))
    ax2.set_xticklabels(tickers, rotation=45, ha='right')
    ax2.grid(True, alpha=0.3, axis='y')
    
    # Adicionar valores nas barras
    for bar, freq in zip(bars, frequencias):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                str(freq), ha='center', va='bottom', fontweight='bold')
    
    # Gráfico 3: Correlações mais baixas (top 20 pares)
    top_20_pares = pares_baixa_correlacao[:20]
    labels_pares = [f"{par['Acao1']}-{par['Acao2']}" for par in top_20_pares]
    correlacoes_top20 = [par['Correlacao'] for par in top_20_pares]
    
    cores = ['green' if c >= 0 else 'red' for c in correlacoes_top20]
    
    ax3.barh(range(len(labels_pares)), correlacoes_top20, color=cores, alpha=0.7)
    ax3.set_title('TOP 20 - Pares com Menor Correlação', fontweight='bold')
    ax3.set_xlabel('Correlação')
    ax3.set_yticks(range(len(labels_pares)))
    ax3.set_yticklabels(labels_pares, fontsize=8)
    ax3.grid(True, alpha=0.3, axis='x')
    ax3.axvline(x=0, color='black', linestyle='-', alpha=0.5)
    
    # Gráfico 4: Estatísticas resumo
    stats_labels = ['Correlação\nMédia', 'Correlação\nMediana', 'Desvio\nPadrão']
    stats_values = [stats_correlacao['media'], stats_correlacao['mediana'], stats_correlacao['std']]
    
    bars4 = ax4.bar(stats_labels, stats_values, color=['blue', 'orange', 'green'], alpha=0.7)
    ax4.set_title('Estatísticas das Correlações', fontweight='bold')
    ax4.set_ylabel('Valor')
    ax4.grid(True, alpha=0.3, axis='y')
    
    # Adicionar valores nas barras
    for bar, val in zip(bars4, stats_values):
        ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{val:.3f}', ha='center', va='bottom', fontweight='bold')
    
    plt.suptitle('Análise de Correlação - Ações Brasileiras', fontsize=18, fontweight='bold')
    plt.tight_layout()

    # Ensure directory exists
    import os
    os.makedirs('results/figures/correlation_analysis', exist_ok=True)

    plt.savefig('results/figures/correlation_analysis/analise_correlacao_completa.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("   💾 results/figures/correlation_analysis/analise_correlacao_completa.png")

def main():
    print("🔍 ANÁLISE DE CORRELAÇÃO - AÇÕES BRASILEIRAS")
    print("="*60)
    print(f"📊 Analisando {len(ACOES_ANALISE)} ações brasileiras")
    print("🎯 Objetivos:")
    print("   • Calcular correlações entre todas as ações")
    print("   • Criar mapa de calor visual")
    print("   • Identificar pares com baixa correlação (≤0.3)")
    print("   • Sugerir ações para diversificação")
    print("   • Exportar resultados para CSV")
    
    confirma = input("\nDeseja continuar? (s/N): ").strip().lower()
    if confirma != 's':
        print("❌ Cancelado pelo usuário")
        return
    
    # 1. Obter dados
    retornos, acoes_validas = obter_dados_retornos(ACOES_ANALISE)
    
    if retornos is None or len(acoes_validas) < 10:
        print("❌ Dados insuficientes para análise!")
        return
    
    # 2. Criar mapa de calor
    correlacao = criar_mapa_calor_correlacao(retornos, acoes_validas)
    
    # 3. Analisar correlações
    pares_baixa_correlacao, stats_correlacao = analisar_correlacoes(correlacao, acoes_validas)

    # 4. Identificar ações para diversificação (com eliminação de alta correlação)
    melhores_diversificacao, acoes_eliminadas = identificar_acoes_diversificacao(
        pares_baixa_correlacao, correlacao, acoes_validas)

    # 5. Salvar resultados
    salvar_resultados_csv(pares_baixa_correlacao, melhores_diversificacao, acoes_eliminadas, stats_correlacao)
    
    # 6. Criar gráficos de análise
    criar_graficos_analise(pares_baixa_correlacao, melhores_diversificacao, stats_correlacao)
    
    # 7. Relatório final
    print("\n" + "="*80)
    print("📈 RELATÓRIO FINAL - ANÁLISE DE CORRELAÇÃO")
    print("="*80)
    
    print(f"\n🔢 ESTATÍSTICAS GERAIS:")
    print(f"   Ações analisadas: {len(acoes_validas)}")
    print(f"   Pares com baixa correlação (≤0.3): {len(pares_baixa_correlacao)}")
    print(f"   Correlação média: {stats_correlacao['media']:.3f}")
    print(f"   Correlação mediana: {stats_correlacao['mediana']:.3f}")

    print(f"\n🎯 PROCESSO DE SELEÇÃO:")
    print(f"   Ações selecionadas para diversificação: {len(melhores_diversificacao)}")
    print(f"   Ações eliminadas por alta correlação (>0.9): {len(acoes_eliminadas)}")

    print(f"\n🎯 TOP 10 AÇÕES SELECIONADAS PARA DIVERSIFICAÇÃO:")
    for i, acao in enumerate(melhores_diversificacao[:10], 1):
        print(f"{i:2d}. {acao['Ticker']:8s} - {acao['Nome']:20s} "
              f"({acao['Frequencia_Baixa_Correlacao']} pares)")

    if acoes_eliminadas:
        print(f"\n� TOP 5 AÇÕES ELIMINADAS POR ALTA CORRELAÇÃO:")
        for i, acao in enumerate(acoes_eliminadas[:5], 1):
            print(f"{i:2d}. {acao['Ticker']:8s} - {acao['Motivo']}")

    print(f"\n�🔗 TOP 10 PARES COM MENOR CORRELAÇÃO:")
    for i, par in enumerate(pares_baixa_correlacao[:10], 1):
        print(f"{i:2d}. {par['Acao1']:8s} x {par['Acao2']:8s} "
              f"= {par['Correlacao']:+.3f}")

    print(f"\n📁 ARQUIVOS GERADOS:")
    print("   • results/figures/correlation_analysis/mapa_calor_correlacao.png")
    print("   • results/figures/correlation_analysis/analise_correlacao_completa.png")
    print("   • results/csv/correlation_data/pares_baixa_correlacao.csv")
    print("   • results/csv/correlation_data/acoes_diversificacao.csv")
    if acoes_eliminadas:
        print("   • results/csv/correlation_data/acoes_eliminadas_alta_correlacao.csv")
    print("   • results/csv/correlation_data/estatisticas_correlacao.csv")

    print(f"\n✅ Análise de correlação concluída!")
    print(f"\n💡 DICA: Use as ações do arquivo 'results/csv/correlation_data/acoes_diversificacao.csv'")
    print("   para montar um portfólio diversificado!")

if __name__ == "__main__":
    main()
