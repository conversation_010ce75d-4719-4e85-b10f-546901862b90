#!/usr/bin/env python3
"""
Demonstração visual da funcionalidade de data de corte
Mostra lado a lado a diferença entre usar dados completos vs dados com corte
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import warnings
import os
from datetime import datetime, timedelta
from analise_kalman_acoes_diversificadas import obter_dados_com_kalman_spread

warnings.filterwarnings('ignore')

# Configurar matplotlib
import matplotlib
matplotlib.use('Agg')

def criar_comparacao_visual(ticker, nome, data_corte_dias=90):
    """
    Cria comparação visual entre dados completos e com corte
    """
    print(f"🎨 CRIANDO COMPARAÇÃO VISUAL: {ticker} - {nome}")
    print("="*60)
    
    # Data de corte
    data_corte = datetime.now() - timedelta(days=data_corte_dias)
    data_corte_str = data_corte.strftime('%Y-%m-%d')
    
    print(f"📅 Data de corte: {data_corte_str} ({data_corte_dias} dias atrás)")
    
    # Obter dados sem corte
    print("📊 Obtendo dados completos...")
    resultado_completo = obter_dados_com_kalman_spread(ticker, nome, data_corte=None)
    
    if not resultado_completo:
        print("❌ Falha ao obter dados completos")
        return False
    
    if len(resultado_completo) == 4:
        dados_completo, previsoes_completo, _, _ = resultado_completo
    else:
        dados_completo, previsoes_completo, _ = resultado_completo
    
    # Obter dados com corte
    print("📊 Obtendo dados com corte...")
    resultado_cortado = obter_dados_com_kalman_spread(ticker, nome, data_corte=data_corte)
    
    if not resultado_cortado:
        print("❌ Falha ao obter dados com corte")
        return False
    
    if len(resultado_cortado) == 4:
        dados_cortado, previsoes_cortado, _, data_corte_usada = resultado_cortado
    else:
        dados_cortado, previsoes_cortado, _ = resultado_cortado
        data_corte_usada = data_corte
    
    # Criar gráfico de comparação
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(18, 14))
    
    # Gráfico 1: Dados completos
    ax1.plot(dados_completo.index, dados_completo['Close'], 'b-', linewidth=2, 
             label='Preço Real', alpha=0.8)
    ax1.plot(dados_completo.index, dados_completo['Kalman'], 'orange', linewidth=2, 
             label='Filtro Kalman', alpha=0.8)
    ax1.plot(dados_completo.index, dados_completo['MM200'], 'green', linewidth=1.5, 
             label='MM200', alpha=0.7)
    
    # Previsões completas
    if previsoes_completo is not None:
        ultima_data_completo = dados_completo.index[-1]
        datas_futuras_completo = pd.date_range(
            start=ultima_data_completo + timedelta(days=1),
            periods=len(previsoes_completo), freq='D'
        )
        ax1.plot(datas_futuras_completo, previsoes_completo, 'purple', 
                linestyle='--', linewidth=2, label='Previsão (dados completos)', alpha=0.8)
    
    ax1.set_title(f'{nome} ({ticker.replace(".SA", "")}) - DADOS COMPLETOS\n'
                  f'Filtro treinado com todos os dados disponíveis', 
                  fontsize=14, fontweight='bold')
    ax1.set_ylabel('Preço (R$)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Gráfico 2: Dados com corte
    ax2.plot(dados_cortado.index, dados_cortado['Close'], 'b-', linewidth=2, 
             label='Preço Real (completo)', alpha=0.8)
    
    # Kalman apenas até a data de corte
    kalman_ate_corte = dados_cortado['Kalman'].copy()
    kalman_ate_corte[dados_cortado.index > data_corte_usada] = np.nan
    ax2.plot(dados_cortado.index, kalman_ate_corte, 'orange', linewidth=2, 
             label='Filtro Kalman (até corte)', alpha=0.8)
    
    ax2.plot(dados_cortado.index, dados_cortado['MM200'], 'green', linewidth=1.5, 
             label='MM200', alpha=0.7)
    
    # Linha de corte
    ax2.axvline(x=data_corte_usada, color='red', linestyle='--', linewidth=2, 
               label=f'Data de Corte ({data_corte_usada.strftime("%Y-%m-%d")})', alpha=0.8)
    
    # Previsões com corte
    if previsoes_cortado is not None:
        datas_futuras_cortado = pd.date_range(
            start=data_corte_usada + timedelta(days=1),
            periods=len(previsoes_cortado), freq='D'
        )
        ax2.plot(datas_futuras_cortado, previsoes_cortado, 'purple', 
                linestyle='--', linewidth=2, label='Previsão (dados até corte)', alpha=0.8)
        
        # Conectar ponto de corte com previsão
        dados_ate_corte = dados_cortado[dados_cortado.index <= data_corte_usada]
        if not dados_ate_corte.empty:
            preco_corte = dados_ate_corte['Close'].iloc[-1]
            ax2.plot([data_corte_usada, datas_futuras_cortado[0]], 
                    [preco_corte, previsoes_cortado[0]], 
                    'purple', linestyle=':', alpha=0.6)
    
    # Destacar área após o corte
    ax2.axvspan(data_corte_usada, dados_cortado.index[-1], alpha=0.1, color='red', 
               label='Período de teste (dados reais vs previsão)')
    
    ax2.set_title(f'{nome} ({ticker.replace(".SA", "")}) - COM DATA DE CORTE\n'
                  f'Filtro treinado apenas até {data_corte_usada.strftime("%Y-%m-%d")} | '
                  f'Dados reais completos exibidos para comparação', 
                  fontsize=14, fontweight='bold')
    ax2.set_xlabel('Data')
    ax2.set_ylabel('Preço (R$)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Salvar gráfico
    os.makedirs('results/figures/comparacao_visual', exist_ok=True)
    arquivo = f'results/figures/comparacao_visual/comparacao_{ticker.replace(".SA", "")}.png'
    plt.savefig(arquivo, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"📈 Gráfico de comparação salvo: {arquivo}")
    
    # Calcular métricas de comparação
    print(f"\n📊 ANÁLISE COMPARATIVA:")
    print(f"Último preço real: R$ {dados_cortado['Close'].iloc[-1]:.2f}")
    
    if previsoes_completo is not None:
        print(f"Previsão (dados completos): R$ {previsoes_completo[-1]:.2f} "
              f"({((previsoes_completo[-1] / dados_completo['Close'].iloc[-1]) - 1) * 100:+.1f}%)")
    
    if previsoes_cortado is not None:
        preco_na_data_corte = dados_cortado[dados_cortado.index <= data_corte_usada]['Close'].iloc[-1]
        print(f"Previsão (dados até corte): R$ {previsoes_cortado[-1]:.2f} "
              f"({((previsoes_cortado[-1] / preco_na_data_corte) - 1) * 100:+.1f}%)")
        
        # Calcular precisão se há dados reais após o corte
        dados_pos_corte = dados_cortado[dados_cortado.index > data_corte_usada]
        if len(dados_pos_corte) >= len(previsoes_cortado):
            precos_reais_pos_corte = dados_pos_corte['Close'].iloc[:len(previsoes_cortado)].values
            erro_absoluto = np.abs(previsoes_cortado - precos_reais_pos_corte)
            mape = np.mean((erro_absoluto / precos_reais_pos_corte) * 100)
            print(f"Precisão da previsão: MAPE = {mape:.2f}%")
    
    return True

def main():
    """
    Demonstração principal
    """
    print("🎨 DEMONSTRAÇÃO VISUAL - COMPARAÇÃO DE DADOS COM E SEM CORTE")
    print("="*70)
    print("Este script cria gráficos lado a lado mostrando:")
    print("1. Análise com dados completos (sem corte)")
    print("2. Análise com data de corte (mostrando dados reais completos)")
    print()
    print("🎯 OBJETIVO: Visualizar como o filtro se comporta quando limitado")
    print("   a dados históricos vs quando pode ver todos os dados.")
    print()
    
    # Ações para demonstração
    acoes_demo = [
        ("PETR4.SA", "Petrobras"),
        ("VALE3.SA", "Vale"),
        ("ITUB4.SA", "Itaú Unibanco")
    ]
    
    print(f"📋 Demonstrando com {len(acoes_demo)} ações")
    print("⏱️ Tempo estimado: 2-3 minutos")
    
    confirma = input("\nDeseja continuar? (s/N): ").strip().lower()
    if confirma != 's':
        print("❌ Cancelado pelo usuário")
        return
    
    sucessos = 0
    
    for i, (ticker, nome) in enumerate(acoes_demo, 1):
        print(f"\n[{i}/{len(acoes_demo)}] ", end="")
        
        if criar_comparacao_visual(ticker, nome, data_corte_dias=90):
            sucessos += 1
        
        print()
    
    print(f"\n✅ Demonstração concluída!")
    print(f"   Sucessos: {sucessos}/{len(acoes_demo)}")
    print(f"📁 Gráficos salvos em: results/figures/comparacao_visual/")
    print()
    print("💡 INTERPRETAÇÃO DOS GRÁFICOS:")
    print("   • Gráfico superior: Filtro treinado com todos os dados")
    print("   • Gráfico inferior: Filtro treinado apenas até a data de corte")
    print("   • Área vermelha: Período onde podemos comparar previsão vs realidade")
    print("   • Linha vermelha tracejada: Data limite do treinamento")
    print()
    print("🔍 OBSERVE:")
    print("   • Como as previsões diferem entre os dois cenários")
    print("   • A precisão das previsões na área de teste")
    print("   • O comportamento do filtro com dados limitados")

if __name__ == "__main__":
    main()
