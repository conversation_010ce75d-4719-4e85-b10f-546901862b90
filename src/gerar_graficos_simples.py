#!/usr/bin/env python3
"""
Script simples para gerar gráficos das ações brasileiras - 12 meses
Foco em gerar e salvar os gráficos de forma garantida
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
import os
warnings.filterwarnings('ignore')

# Configurar matplotlib para salvar gráficos
import matplotlib
matplotlib.use('Agg')  # Backend para salvar sem necessidade de display

# Opção 1: Principais ações (mais líquidas)
ACOES_PRINCIPAIS = [
    ('PETR4.SA', 'Petrobras PN'),
    ('PETR3.SA', 'Petrobras ON'),
    ('VALE3.SA', 'Vale ON'),
    ('ITUB4.SA', 'Itaú Unibanco PN'),
    ('BBDC4.SA', 'Bradesco PN'),
    ('ABEV3.SA', 'Ambev ON'),
    ('WEGE3.SA', 'WEG ON'),
    ('RENT3.SA', 'Localiza ON'),
    ('LREN3.SA', 'Lojas Renner ON'),
    ('MGLU3.SA', 'Magazine Luiza ON'),
    ('SUZB3.SA', 'Suzano ON'),
    ('JBSS3.SA', 'JBS ON'),
    ('B3SA3.SA', 'B3 ON'),
    ('RAIL3.SA', 'Rumo ON'),
    ('CCRO3.SA', 'CCR ON'),
    ('ELET3.SA', 'Eletrobras ON'),
    ('BBAS3.SA', 'Banco do Brasil ON'),
    ('RADL3.SA', 'Raia Drogasil ON'),
    ('CSAN3.SA', 'Cosan ON'),
    ('GGBR4.SA', 'Gerdau PN')
]

# Opção 2: Todas as 73 ações solicitadas
TODAS_ACOES_BRASILEIRAS = [
    ('ABEV3.SA', 'AMBEV S/A'),
    ('AZUL4.SA', 'AZUL'),
    ('B3SA3.SA', 'B3'),
    ('BBAS3.SA', 'BRASIL'),
    ('BBDC3.SA', 'BRADESCO'),
    ('BBDC4.SA', 'BRADESCO'),
    ('BBSE3.SA', 'BBSEGURIDADE'),
    ('BEEF3.SA', 'MINERVA'),
    ('BPAC11.SA', 'BTGP BANCO'),
    ('BRAP4.SA', 'BRADESPAR'),
    ('BRDT3.SA', 'PETROBRAS BR'),
    ('BRFS3.SA', 'BRF SA'),
    ('BRKM5.SA', 'BRASKEM'),
    ('BRML3.SA', 'BR MALLS PAR'),
    ('BTOW3.SA', 'B2W DIGITAL'),
    ('CCRO3.SA', 'CCR SA'),
    ('CIEL3.SA', 'CIELO'),
    ('CMIG4.SA', 'CEMIG'),
    ('COGN3.SA', 'COGNA ON'),
    ('CPFE3.SA', 'CPFL ENERGIA'),
    ('CRFB3.SA', 'CARREFOUR BR'),
    ('CSAN3.SA', 'COSAN'),
    ('CSNA3.SA', 'SID NACIONAL'),
    ('CVCB3.SA', 'CVC BRASIL'),
    ('CYRE3.SA', 'CYRELA REALT'),
    ('ECOR3.SA', 'ECORODOVIAS'),
    ('EGIE3.SA', 'ENGIE BRASIL'),
    ('ELET3.SA', 'ELETROBRAS'),
    ('ELET6.SA', 'ELETROBRAS'),
    ('EMBR3.SA', 'EMBRAER'),
    ('ENBR3.SA', 'ENERGIAS BR'),
    ('ENGI11.SA', 'ENERGISA'),
    ('EQTL3.SA', 'EQUATORIAL'),
    ('EZTC3.SA', 'EZTEC'),
    ('FLRY3.SA', 'FLEURY'),
    ('GGBR4.SA', 'GERDAU'),
    ('GNDI3.SA', 'INTERMEDICA'),
    ('GOAU4.SA', 'GERDAU MET'),
    ('GOLL4.SA', 'GOL'),
    ('HAPV3.SA', 'HAPVIDA'),
    ('HGTX3.SA', 'CIA HERING'),
    ('HYPE3.SA', 'HYPERA'),
    ('IGTA3.SA', 'IGUATEMI'),
    ('IRBR3.SA', 'IRBBRASIL RE'),
    ('ITSA4.SA', 'ITAUSA'),
    ('ITUB4.SA', 'ITAUUNIBANCO'),
    ('JBSS3.SA', 'JBS'),
    ('KLBN11.SA', 'KLABIN S/A'),
    ('LAME4.SA', 'LOJAS AMERIC'),
    ('LREN3.SA', 'LOJAS RENNER'),
    ('MGLU3.SA', 'MAGAZ LUIZA'),
    ('MRFG3.SA', 'MARFRIG'),
    ('MRVE3.SA', 'MRV'),
    ('MULT3.SA', 'MULTIPLAN'),
    ('NTCO3.SA', 'GRUPO NATURA'),
    ('PCAR3.SA', 'P.ACUCAR-CBD'),
    ('PETR3.SA', 'PETROBRAS'),
    ('PETR4.SA', 'PETROBRAS'),
    ('PRIO3.SA', 'PETRORIO'),
    ('QUAL3.SA', 'QUALICORP'),
    ('RADL3.SA', 'RAIADROGASIL'),
    ('RAIL3.SA', 'RUMO S.A.'),
    ('RENT3.SA', 'LOCALIZA'),
    ('SANB11.SA', 'SANTANDER BR'),
    ('SBSP3.SA', 'SABESP'),
    ('SULA11.SA', 'SUL AMERICA'),
    ('SUZB3.SA', 'SUZANO S.A.'),
    ('TAEE11.SA', 'TAESA'),
    ('TIMS3.SA', 'TIM'),
    ('TOTS3.SA', 'TOTVS'),
    ('UGPA3.SA', 'ULTRAPAR'),
    ('USIM5.SA', 'USIMINAS'),
    ('VALE3.SA', 'VALE'),
    ('VIVT4.SA', 'TELEF BRASIL'),
    ('VVAR3.SA', 'VIAVAREJO'),
    ('WEGE3.SA', 'WEG'),
    ('YDUQ3.SA', 'YDUQS PART')
]

def obter_dados_acao(ticker, nome):
    """
    Obtém dados de 12 meses de uma ação
    """
    try:
        print(f"📊 Obtendo dados: {ticker.replace('.SA', '')} - {nome}")
        
        stock = yf.Ticker(ticker)
        dados = stock.history(period="1y")
        
        if dados.empty:
            print(f"   ⚠️ Sem dados disponíveis")
            return None
            
        print(f"   ✅ {len(dados)} dias de dados obtidos")
        return dados
        
    except Exception as e:
        print(f"   ❌ Erro: {str(e)}")
        return None

def criar_grafico_acao(ticker, nome, dados):
    """
    Cria gráfico individual de uma ação
    """
    # Criar figura com 2 subplots
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
    
    # Calcular estatísticas
    preco_inicial = dados['Close'].iloc[0]
    preco_final = dados['Close'].iloc[-1]
    preco_max = dados['Close'].max()
    preco_min = dados['Close'].min()
    performance = ((preco_final / preco_inicial) - 1) * 100
    
    # Gráfico 1: Preço
    ax1.plot(dados.index, dados['Close'], linewidth=2, color='#1f77b4', label='Preço de Fechamento')
    ax1.fill_between(dados.index, dados['Low'], dados['High'], alpha=0.2, color='#ff7f0e', label='Faixa Diária')
    
    ax1.set_title(f'{nome} ({ticker.replace(".SA", "")}) - Últimos 12 Meses', 
                  fontsize=16, fontweight='bold')
    ax1.set_ylabel('Preço (R$)', fontsize=12)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Adicionar estatísticas
    stats_text = f'Preço Inicial: R$ {preco_inicial:.2f}\n'
    stats_text += f'Preço Atual: R$ {preco_final:.2f}\n'
    stats_text += f'Performance: {performance:+.1f}%\n'
    stats_text += f'Máximo: R$ {preco_max:.2f}\n'
    stats_text += f'Mínimo: R$ {preco_min:.2f}'
    
    cor_box = 'lightgreen' if performance >= 0 else 'lightcoral'
    ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes, 
             fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor=cor_box, alpha=0.8))
    
    # Gráfico 2: Volume
    ax2.bar(dados.index, dados['Volume']/1e6, alpha=0.7, color='orange', width=0.8)
    ax2.set_title('Volume de Negociação', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Data', fontsize=12)
    ax2.set_ylabel('Volume (Milhões)', fontsize=12)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()

    # Ensure directory exists
    import os
    os.makedirs('../results/figures/individual_stocks', exist_ok=True)

    # Salvar gráfico
    nome_arquivo = f"../results/figures/individual_stocks/acao_{ticker.replace('.SA', '')}_12meses.png"
    plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight')
    plt.close()  # Fechar para liberar memória

    print(f"   💾 Gráfico salvo: {nome_arquivo}")
    
    return {
        'ticker': ticker,
        'nome': nome,
        'performance': performance,
        'preco_inicial': preco_inicial,
        'preco_final': preco_final,
        'preco_max': preco_max,
        'preco_min': preco_min
    }

def criar_grafico_comparativo(resultados):
    """
    Cria gráfico comparativo de todas as ações
    """
    print("\n📊 Criando gráfico comparativo...")
    
    # Ordenar por performance
    resultados_ordenados = sorted(resultados, key=lambda x: x['performance'], reverse=True)
    
    # Criar gráfico de barras com performance
    fig, ax = plt.subplots(figsize=(16, 10))
    
    tickers = [r['ticker'].replace('.SA', '') for r in resultados_ordenados]
    performances = [r['performance'] for r in resultados_ordenados]
    
    # Cores: verde para positivo, vermelho para negativo
    cores = ['green' if p >= 0 else 'red' for p in performances]
    
    bars = ax.bar(range(len(tickers)), performances, color=cores, alpha=0.7)
    
    # Configurar gráfico
    ax.set_title('Performance das Ações Brasileiras - Últimos 12 Meses', 
                 fontsize=16, fontweight='bold')
    ax.set_xlabel('Ações', fontsize=12)
    ax.set_ylabel('Performance (%)', fontsize=12)
    ax.set_xticks(range(len(tickers)))
    ax.set_xticklabels(tickers, rotation=45, ha='right')
    ax.grid(True, alpha=0.3, axis='y')
    ax.axhline(y=0, color='black', linestyle='-', alpha=0.8)
    
    # Adicionar valores nas barras
    for i, (bar, perf) in enumerate(zip(bars, performances)):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + (1 if height >= 0 else -3),
                f'{perf:.1f}%', ha='center', va='bottom' if height >= 0 else 'top',
                fontsize=8, fontweight='bold')
    
    plt.tight_layout()

    # Ensure directory exists
    import os
    os.makedirs('../results/figures/comparative_analysis', exist_ok=True)

    plt.savefig('../results/figures/comparative_analysis/comparativo_acoes_brasileiras_12meses.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("   💾 Gráfico comparativo salvo: ../results/figures/comparative_analysis/comparativo_acoes_brasileiras_12meses.png")

def exibir_relatorio(resultados):
    """
    Exibe relatório final das análises
    """
    print("\n" + "="*80)
    print("📈 RELATÓRIO FINAL - AÇÕES BRASILEIRAS (12 MESES)")
    print("="*80)
    
    # Ordenar por performance
    resultados_ordenados = sorted(resultados, key=lambda x: x['performance'], reverse=True)
    
    print(f"{'Rank':<4} {'Ticker':<8} {'Nome':<25} {'Performance':<12} {'Preço Atual':<12}")
    print("-" * 80)
    
    for i, resultado in enumerate(resultados_ordenados, 1):
        emoji = "🟢" if resultado['performance'] >= 0 else "🔴"
        print(f"{i:<4} {emoji} {resultado['ticker'].replace('.SA', ''):<6} "
              f"{resultado['nome'][:24]:<25} {resultado['performance']:>+8.1f}% "
              f"R$ {resultado['preco_final']:>8.2f}")
    
    # Estatísticas gerais
    performances = [r['performance'] for r in resultados]
    media_performance = np.mean(performances)
    melhor_performance = max(performances)
    pior_performance = min(performances)
    acoes_positivas = len([p for p in performances if p >= 0])
    
    print("\n" + "="*80)
    print("📊 ESTATÍSTICAS GERAIS")
    print("="*80)
    print(f"Total de ações analisadas: {len(resultados)}")
    print(f"Ações com performance positiva: {acoes_positivas} ({acoes_positivas/len(resultados)*100:.1f}%)")
    print(f"Performance média: {media_performance:.1f}%")
    print(f"Melhor performance: {melhor_performance:.1f}%")
    print(f"Pior performance: {pior_performance:.1f}%")

def main():
    print("🚀 GERADOR DE GRÁFICOS - AÇÕES BRASILEIRAS (12 MESES)")
    print("="*70)

    print("\nOpções:")
    print("1. Principais ações (20 ações mais líquidas)")
    print("2. Todas as ações solicitadas (73 ações)")

    opcao = input("\nEscolha uma opção (1-2): ").strip()

    if opcao == "2":
        acoes_escolhidas = TODAS_ACOES_BRASILEIRAS
        print(f"\n📋 Serão analisadas {len(acoes_escolhidas)} ações (TODAS)")
    else:
        acoes_escolhidas = ACOES_PRINCIPAIS
        print(f"\n📋 Serão analisadas {len(acoes_escolhidas)} ações (PRINCIPAIS)")

    print("📊 Para cada ação será gerado:")
    print("   • Gráfico individual com preço e volume")
    print("   • Estatísticas de performance")
    print("📊 Ao final será gerado:")
    print("   • Gráfico comparativo de todas as ações")
    print("   • Relatório completo")

    input("\nPressione Enter para começar...")

    resultados = []

    print(f"\n📈 Iniciando análise de {len(acoes_escolhidas)} ações...")

    for i, (ticker, nome) in enumerate(acoes_escolhidas, 1):
        print(f"\n[{i}/{len(acoes_escolhidas)}] Processando {ticker.replace('.SA', '')}")

        # Obter dados
        dados = obter_dados_acao(ticker, nome)

        if dados is not None:
            # Criar gráfico e obter estatísticas
            resultado = criar_grafico_acao(ticker, nome, dados)
            resultados.append(resultado)
        else:
            print(f"   ⚠️ Pulando {ticker} - sem dados")
    
    if resultados:
        # Criar gráfico comparativo
        criar_grafico_comparativo(resultados)
        
        # Exibir relatório
        exibir_relatorio(resultados)
        
        # Listar arquivos gerados
        print(f"\n📁 ARQUIVOS GERADOS:")
        arquivos_png = sorted([f for f in os.listdir('.') if f.endswith('.png') and 'acao_' in f])
        for arquivo in arquivos_png:
            print(f"   • {arquivo}")
        print(f"   • comparativo_acoes_brasileiras_12meses.png")
        
        print(f"\n✅ Processo concluído! {len(resultados)} gráficos gerados.")
        
    else:
        print("\n❌ Nenhum dado foi obtido com sucesso!")

if __name__ == "__main__":
    main()
