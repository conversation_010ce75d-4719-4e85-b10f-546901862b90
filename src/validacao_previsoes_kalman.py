#!/usr/bin/env python3
"""
Script para validar a precisão das previsões do filtro de Kalman
Compara previsões feitas em datas passadas com os valores reais observados
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import warnings
import os
from datetime import datetime, timedelta
from analise_kalman_acoes_diversificadas import obter_dados_com_kalman_spread

warnings.filterwarnings('ignore')

# Configurar matplotlib
import matplotlib
matplotlib.use('Agg')

def validar_previsoes_kalman(ticker, nome, dias_teste=60, dias_previsao=20):
    """
    Valida a precisão das previsões do filtro de Kalman
    
    Args:
        ticker: Símbolo da ação
        nome: Nome da empresa
        dias_teste: Quantos dias atrás fazer a previsão
        dias_previsao: Quantos dias à frente prever
    """
    print(f"🔍 VALIDAÇÃO DE PREVISÕES: {ticker} - {nome}")
    print("="*60)
    
    try:
        # Data de corte para teste (X dias atrás)
        data_corte = datetime.now() - timedelta(days=dias_teste)
        data_corte_str = data_corte.strftime('%Y-%m-%d')
        
        print(f"📅 Data de corte: {data_corte_str}")
        print(f"🔮 Previsão para: {dias_previsao} dias à frente")
        print(f"📊 Período de validação: {dias_teste} dias")
        
        # Obter dados com corte para fazer previsão
        resultado_previsao = obter_dados_com_kalman_spread(ticker, nome, data_corte=data_corte)
        
        if not resultado_previsao:
            print("❌ Falha ao obter dados para previsão")
            return None
        
        dados_treino, previsoes, kf = resultado_previsao
        
        # Obter dados completos para comparação
        resultado_completo = obter_dados_com_kalman_spread(ticker, nome, data_corte=None)
        
        if not resultado_completo:
            print("❌ Falha ao obter dados completos")
            return None
        
        dados_completos, _, _ = resultado_completo
        
        # Encontrar dados reais após a data de corte
        dados_reais = dados_completos[dados_completos.index > data_corte]
        
        if len(dados_reais) < dias_previsao:
            print(f"⚠️ Dados reais insuficientes: {len(dados_reais)} < {dias_previsao}")
            dias_previsao = len(dados_reais)
        
        # Comparar previsões com valores reais
        precos_reais = dados_reais['Close'].iloc[:dias_previsao].values
        previsoes_array = previsoes[:len(precos_reais)]
        
        # Calcular métricas de erro
        erro_absoluto = np.abs(previsoes_array - precos_reais)
        erro_percentual = (erro_absoluto / precos_reais) * 100
        
        mae = np.mean(erro_absoluto)  # Mean Absolute Error
        mape = np.mean(erro_percentual)  # Mean Absolute Percentage Error
        rmse = np.sqrt(np.mean((previsoes_array - precos_reais)**2))  # Root Mean Square Error
        
        # Calcular correlação
        correlacao = np.corrcoef(previsoes_array, precos_reais)[0, 1]
        
        print(f"\n📈 RESULTADOS DA VALIDAÇÃO")
        print("-" * 40)
        print(f"Dias comparados: {len(precos_reais)}")
        print(f"Preço inicial: R$ {dados_treino['Close'].iloc[-1]:.2f}")
        print(f"Preço final real: R$ {precos_reais[-1]:.2f}")
        print(f"Preço final previsto: R$ {previsoes_array[-1]:.2f}")
        print()
        print(f"📊 MÉTRICAS DE ERRO:")
        print(f"MAE (Erro Absoluto Médio): R$ {mae:.2f}")
        print(f"MAPE (Erro Percentual Médio): {mape:.2f}%")
        print(f"RMSE (Raiz do Erro Quadrático): R$ {rmse:.2f}")
        print(f"Correlação: {correlacao:.3f}")
        
        # Classificar precisão
        if mape < 2:
            precisao = "Excelente"
        elif mape < 5:
            precisao = "Boa"
        elif mape < 10:
            precisao = "Moderada"
        else:
            precisao = "Baixa"
        
        print(f"Classificação: {precisao}")
        
        # Criar gráfico de comparação
        criar_grafico_validacao(ticker, nome, dados_treino, previsoes_array, precos_reais, 
                               data_corte, mae, mape, correlacao)
        
        return {
            'ticker': ticker,
            'nome': nome,
            'data_corte': data_corte_str,
            'dias_previsao': len(precos_reais),
            'mae': mae,
            'mape': mape,
            'rmse': rmse,
            'correlacao': correlacao,
            'precisao': precisao,
            'preco_inicial': dados_treino['Close'].iloc[-1],
            'preco_final_real': precos_reais[-1],
            'preco_final_previsto': previsoes_array[-1]
        }
        
    except Exception as e:
        print(f"❌ Erro na validação: {e}")
        return None

def criar_grafico_validacao(ticker, nome, dados_treino, previsoes, precos_reais, data_corte, mae, mape, correlacao):
    """
    Cria gráfico comparando previsões com valores reais
    """
    try:
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))
        
        # Gráfico 1: Série temporal completa
        ax1.plot(dados_treino.index, dados_treino['Close'], 'b-', label='Preços Históricos', linewidth=2)
        ax1.axvline(x=data_corte, color='red', linestyle='--', alpha=0.7, label='Data de Corte')
        
        # Criar datas para previsões
        ultima_data = dados_treino.index[-1]
        datas_previsao = pd.date_range(start=ultima_data + timedelta(days=1), periods=len(previsoes), freq='D')
        
        ax1.plot(datas_previsao, previsoes, 'g--', label='Previsões Kalman', linewidth=2, marker='o', markersize=4)
        ax1.plot(datas_previsao[:len(precos_reais)], precos_reais, 'r-', label='Preços Reais', linewidth=2, marker='s', markersize=4)
        
        ax1.set_title(f'{ticker.replace(".SA", "")} - {nome}\nValidação de Previsões Kalman', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Preço (R$)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Gráfico 2: Comparação direta previsões vs reais
        ax2.scatter(previsoes[:len(precos_reais)], precos_reais, alpha=0.7, s=50)
        
        # Linha de referência (previsão perfeita)
        min_val = min(min(previsoes[:len(precos_reais)]), min(precos_reais))
        max_val = max(max(previsoes[:len(precos_reais)]), max(precos_reais))
        ax2.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.7, label='Previsão Perfeita')
        
        ax2.set_xlabel('Previsões Kalman (R$)')
        ax2.set_ylabel('Preços Reais (R$)')
        ax2.set_title(f'Previsões vs Realidade\nMAE: R${mae:.2f} | MAPE: {mape:.2f}% | Correlação: {correlacao:.3f}')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Salvar gráfico
        os.makedirs('results/figures/validacao_kalman', exist_ok=True)
        arquivo = f'results/figures/validacao_kalman/validacao_{ticker.replace(".SA", "")}.png'
        plt.savefig(arquivo, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📈 Gráfico de validação salvo: {arquivo}")
        
    except Exception as e:
        print(f"⚠️ Erro ao criar gráfico: {e}")

def main():
    """
    Executa validação para múltiplas ações
    """
    print("🚀 VALIDAÇÃO DE PREVISÕES - FILTRO DE KALMAN")
    print("="*60)
    print("Este script testa a precisão das previsões do filtro de Kalman")
    print("comparando previsões feitas no passado com os valores reais observados.")
    print()
    
    # Ações para teste
    acoes_teste = [
        ("PETR4.SA", "Petrobras"),
        ("VALE3.SA", "Vale"),
        ("ITUB4.SA", "Itaú Unibanco"),
        ("BBDC4.SA", "Bradesco"),
        ("ABEV3.SA", "Ambev")
    ]
    
    print(f"📋 Testando {len(acoes_teste)} ações")
    print("⏱️ Tempo estimado: 3-5 minutos")
    
    confirma = input("\nDeseja continuar? (s/N): ").strip().lower()
    if confirma != 's':
        print("❌ Cancelado pelo usuário")
        return
    
    resultados = []
    
    for i, (ticker, nome) in enumerate(acoes_teste, 1):
        print(f"\n[{i}/{len(acoes_teste)}] ", end="")
        
        resultado = validar_previsoes_kalman(ticker, nome, dias_teste=60, dias_previsao=20)
        
        if resultado:
            resultados.append(resultado)
        
        print()
    
    # Resumo final
    if resultados:
        print("\n📊 RESUMO GERAL DA VALIDAÇÃO")
        print("="*60)
        
        df_resultados = pd.DataFrame(resultados)
        
        print(f"Ações testadas: {len(resultados)}")
        print(f"MAPE médio: {df_resultados['mape'].mean():.2f}%")
        print(f"Correlação média: {df_resultados['correlacao'].mean():.3f}")
        print(f"MAE médio: R$ {df_resultados['mae'].mean():.2f}")
        
        # Salvar resultados
        os.makedirs('results/csv/validacao_kalman', exist_ok=True)
        csv_path = 'results/csv/validacao_kalman/resultados_validacao.csv'
        df_resultados.to_csv(csv_path, index=False)
        print(f"\n💾 Resultados salvos em: {csv_path}")
        print(f"📁 Gráficos salvos em: results/figures/validacao_kalman/")
        
        # Top performers
        print(f"\n🏆 TOP 3 MELHORES PREVISÕES (menor MAPE):")
        top_3 = df_resultados.nsmallest(3, 'mape')
        for i, row in enumerate(top_3.itertuples(), 1):
            print(f"{i}. {row.ticker.replace('.SA', ''):<6} - MAPE: {row.mape:.2f}% | Correlação: {row.correlacao:.3f}")
    
    else:
        print("\n❌ Nenhuma validação foi concluída com sucesso")

if __name__ == "__main__":
    main()
