#!/usr/bin/env python3
"""
Test script for Twelve Data API - Searching for bid/ask volume data
Using stocks from acoes_diversificacao.csv
"""

import time
import pandas as pd
import requests
from twelvedata import TDClient
from datetime import datetime

# Your Twelve Data API key
API_KEY = "aadf74ae14d74fd7ac40a433dad50f51"

def carregar_acoes_diversificadas():
    """
    Carrega as ações do arquivo CSV de diversificação
    """
    try:
        csv_path = 'results/csv/correlation_data/acoes_diversificacao.csv'
        df = pd.read_csv(csv_path)
        
        # Pegar apenas as primeiras 5 ações para teste (API tem limite de calls)
        acoes = []
        for _, row in df.head(5).iterrows():
            ticker_original = row['Ticker']
            nome = row['Nome']
            acoes.append((ticker_original, nome))
        
        print(f"📋 Carregadas {len(acoes)} ações para teste Twelve Data")
        return acoes
        
    except Exception as e:
        print(f"❌ Erro ao carregar arquivo CSV: {e}")
        return []

def test_us_stocks():
    """
    Ações americanas para teste
    """
    return [
        ('AAPL', 'Apple Inc'),
        ('GOOGL', 'Alphabet Inc'),
        ('MSFT', 'Microsoft Corp'),
        ('TSLA', 'Tesla Inc')
    ]

def test_quote_endpoint(ticker, nome):
    """
    Testa o endpoint de cotação usando chamada direta à API
    """
    print(f"\n🔍 Testando QUOTE para {ticker} ({nome})")

    try:
        url = f"https://api.twelvedata.com/quote?symbol={ticker}&apikey={API_KEY}&format=JSON"
        response = requests.get(url)

        if response.status_code == 200:
            data = response.json()

            # Verificar se há erro na resposta
            if 'code' in data and data['code'] != 200:
                print(f"   ❌ Erro da API: {data.get('message', 'Erro desconhecido')}")
                return False, None

            print("✅ Dados de cotação obtidos!")
            print(f"   Símbolo: {data.get('symbol', 'N/A')}")
            print(f"   Preço: ${data.get('close', 'N/A')}")
            print(f"   Volume: {data.get('volume', 'N/A')}")
            print(f"   Timestamp: {data.get('datetime', 'N/A')}")

            # Verificar TODOS os campos disponíveis
            print(f"   📊 Campos disponíveis: {list(data.keys())}")

            # Procurar especificamente por bid/ask
            bid_ask_found = False
            for key, value in data.items():
                if any(term in key.lower() for term in ['bid', 'ask']):
                    print(f"   🎯 BID/ASK ENCONTRADO: {key} = {value}")
                    bid_ask_found = True

            if not bid_ask_found:
                print("   ❌ Nenhuma informação de BID/ASK encontrada")

            return True, data
        else:
            print(f"   ❌ Erro HTTP: {response.status_code}")
            return False, None

    except Exception as e:
        print(f"   ❌ Erro: {e}")
        return False, None

def test_real_time_price(ticker, nome):
    """
    Testa endpoint de preço em tempo real usando chamada direta
    """
    print(f"\n📊 Testando REAL-TIME PRICE para {ticker} ({nome})")

    try:
        url = f"https://api.twelvedata.com/price?symbol={ticker}&apikey={API_KEY}&format=JSON"
        response = requests.get(url)

        if response.status_code == 200:
            data = response.json()

            if 'code' in data and data['code'] != 200:
                print(f"   ❌ Erro da API: {data.get('message', 'Erro desconhecido')}")
                return False, None

            print("✅ Dados de preço em tempo real obtidos!")
            print(f"   Preço: ${data.get('price', 'N/A')}")
            print(f"   📊 Campos disponíveis: {list(data.keys())}")

            # Procurar por bid/ask
            for key, value in data.items():
                if any(term in key.lower() for term in ['bid', 'ask']):
                    print(f"   🎯 BID/ASK ENCONTRADO: {key} = {value}")

            return True, data
        else:
            print(f"   ❌ Erro HTTP: {response.status_code}")
            return False, None

    except Exception as e:
        print(f"   ❌ Erro: {e}")
        return False, None

def test_additional_endpoints(ticker, nome):
    """
    Testa endpoints adicionais que podem ter bid/ask
    """
    print(f"\n🔬 Testando ENDPOINTS ADICIONAIS para {ticker} ({nome})")

    # Lista de endpoints para testar
    endpoints = [
        {
            "name": "Time Series (1min)",
            "url": f"https://api.twelvedata.com/time_series?symbol={ticker}&interval=1min&apikey={API_KEY}&outputsize=5"
        },
        {
            "name": "Exchange Rate (se for forex)",
            "url": f"https://api.twelvedata.com/exchange_rate?symbol=EUR/USD&apikey={API_KEY}"
        },
        {
            "name": "Market State",
            "url": f"https://api.twelvedata.com/market_state?exchange=NASDAQ&apikey={API_KEY}"
        }
    ]

    for endpoint in endpoints:
        print(f"\n   🔍 {endpoint['name']}")
        try:
            response = requests.get(endpoint['url'])
            if response.status_code == 200:
                data = response.json()

                # Procurar por bid/ask em toda a resposta
                data_str = str(data).lower()
                if 'bid' in data_str or 'ask' in data_str:
                    print(f"      🎯 Possível BID/ASK encontrado!")
                    print(f"      Dados: {data}")
                else:
                    print(f"      ❌ Nenhuma informação BID/ASK")
            else:
                print(f"      ❌ Erro HTTP: {response.status_code}")

        except Exception as e:
            print(f"      ❌ Erro: {e}")

        time.sleep(0.5)

def test_direct_api_calls():
    """
    Testa chamadas diretas à API para endpoints específicos
    """
    print(f"\n🔬 TESTANDO CHAMADAS DIRETAS À API")
    print("=" * 50)
    
    # Endpoints para testar
    endpoints_to_test = [
        {
            "name": "Quote with extended data",
            "url": f"https://api.twelvedata.com/quote?symbol=AAPL&apikey={API_KEY}&format=JSON"
        },
        {
            "name": "Real-time price",
            "url": f"https://api.twelvedata.com/price?symbol=AAPL&apikey={API_KEY}&format=JSON"
        },
        {
            "name": "Market state",
            "url": f"https://api.twelvedata.com/market_state?symbol=AAPL&apikey={API_KEY}&format=JSON"
        },
        {
            "name": "Symbol search",
            "url": f"https://api.twelvedata.com/symbol_search?symbol=AAPL&apikey={API_KEY}&format=JSON"
        }
    ]
    
    for endpoint in endpoints_to_test:
        print(f"\n🔍 Testando: {endpoint['name']}")
        try:
            response = requests.get(endpoint['url'])
            data = response.json()
            
            print(f"   Status: {response.status_code}")
            print(f"   Campos: {list(data.keys()) if isinstance(data, dict) else 'Não é dict'}")
            
            # Procurar por bid/ask em toda a resposta
            data_str = str(data).lower()
            if 'bid' in data_str or 'ask' in data_str:
                print(f"   🎯 Possível BID/ASK encontrado!")
                print(f"   Dados: {data}")
            else:
                print(f"   ❌ Nenhuma informação BID/ASK")
                
        except Exception as e:
            print(f"   ❌ Erro: {e}")
        
        time.sleep(1)

def test_brazilian_stocks():
    """
    Testa ações brasileiras com diferentes formatos
    """
    print(f"\n🇧🇷 TESTANDO AÇÕES BRASILEIRAS")
    print("=" * 50)
    
    # Diferentes formatos para testar
    brazilian_formats = [
        ("PETR4.SA", "Petrobras PN (.SA)"),
        ("PETR4.SAO", "Petrobras PN (.SAO)"),
        ("PETR4", "Petrobras PN (sem sufixo)"),
        ("PBR", "Petrobras ADR"),
        ("VALE3.SA", "Vale (.SA)"),
        ("VALE", "Vale ADR")
    ]
    
    for ticker, nome in brazilian_formats:
        print(f"\n🔍 Testando: {ticker} - {nome}")
        success, data = test_quote_endpoint(ticker, nome)
        if success and data:
            print(f"   ✅ SUCESSO com formato: {ticker}")
        time.sleep(1)

def main():
    """
    Função principal para testar Twelve Data API
    """
    print("🚀 TESTE TWELVE DATA API - BUSCA POR BID/ASK VOLUME")
    print("=" * 60)
    print(f"🔑 Usando API Key: {API_KEY[:8]}...")
    print()
    
    # Teste 1: Ações americanas com múltiplos endpoints
    print("🇺🇸 TESTANDO COM AÇÕES AMERICANAS")
    print("=" * 40)
    
    us_stocks = test_us_stocks()
    success_count = 0
    
    for ticker, nome in us_stocks[:2]:  # Testar apenas 2 para não esgotar API calls
        print(f"\n{'='*50}")
        print(f"🔍 TESTANDO: {ticker} - {nome}")
        print(f"{'='*50}")
        
        # Teste 1: Quote endpoint
        success1, _ = test_quote_endpoint(ticker, nome)
        if success1:
            success_count += 1

        time.sleep(1)

        # Teste 2: Real-time price
        success2, _ = test_real_time_price(ticker, nome)
        if success2:
            success_count += 1

        time.sleep(1)

        # Teste 3: Endpoints adicionais
        test_additional_endpoints(ticker, nome)
            
        time.sleep(2)  # Pausa maior entre ações
    
    # Teste 2: Chamadas diretas à API
    test_direct_api_calls()
    
    # Teste 3: Ações brasileiras
    test_brazilian_stocks()
    
    # Resumo final
    print(f"\n\n📋 RESUMO DOS TESTES TWELVE DATA")
    print("=" * 50)
    print(f"✅ Testes realizados com sucesso: {success_count}")
    print(f"\n🔍 CONCLUSÃO SOBRE BID/ASK VOLUME:")
    print(f"   Twelve Data foi testado em múltiplos endpoints.")
    print(f"   Verificamos: quote, price, market_state, e chamadas diretas.")
    print(f"   Resultado será mostrado acima para cada teste.")
    
    print(f"\n💡 PRÓXIMOS PASSOS:")
    print(f"   Se bid/ask não foi encontrado, considere:")
    print(f"   • Plano premium do Twelve Data")
    print(f"   • Interactive Brokers API")
    print(f"   • APIs de corretoras brasileiras")

if __name__ == "__main__":
    main()
